/* 朗读回答问题页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #667eea;
  border-radius: 50%;
}

.back-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.title-info {
  flex: 1;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

.score-info {
  min-width: 120rpx;
  text-align: right;
}

.score-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

/* 流程指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ddd;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.step.active .step-number {
  background: #667eea;
  color: white;
}

.step.completed .step-number {
  background: #52c41a;
  color: white;
}

.step-label {
  font-size: 24rpx;
  color: #666;
}

.step.active .step-label {
  color: #667eea;
  font-weight: bold;
}

.step.completed .step-label {
  color: #52c41a;
  font-weight: bold;
}

.step-line {
  width: 100rpx;
  height: 4rpx;
  background: #ddd;
  margin: 0 20rpx;
  margin-bottom: 40rpx;
}

.step.completed + .step-line {
  background: #52c41a;
}

/* 通用卡片样式 */
.instruction-card, .article-container, .question-card, .recording-card, .result-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

/* 时间显示 */
.timer-display {
  display: none;
}

/* 说明卡片 */
.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.instruction-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 文章容器 */
.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.article-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
}

.article-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

/* 问题预览 */
.questions-preview {
  margin-top: 30rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.question-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 20rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
}

.question-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
  margin-right: 15rpx;
  min-width: 40rpx;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 录音显示 */
.recording-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.record-time {
  text-align: right;
}

.record-text {
  font-size: 28rpx;
  color: #666;
  padding: 15rpx 25rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 25rpx;
}

/* 录音卡片 */
.recording-icon {
  font-size: 80rpx;
  text-align: center;
  margin-bottom: 20rpx;
  animation: pulse 2s infinite;
}

.recording-icon.recording {
  animation: recording-pulse 1s infinite;
}

.recording-status {
  text-align: center;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 问题准备 */
.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.question-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.question-info, .progress-info {
  font-size: 24rpx;
  color: #666;
}

.current-question {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.prepare-tips {
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  font-style: italic;
}

/* 控制按钮 */
.recording-controls {
  text-align: center;
  margin-top: 30rpx;
}

.control-btn {
  padding: 25rpx 60rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.stop-btn {
  background: #ff4d4f;
  color: white;
}

/* 结果页面 */
.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-icon {
  font-size: 100rpx;
  display: block;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.score-breakdown {
  margin-bottom: 40rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.score-label {
  font-size: 30rpx;
  color: #333;
}

.score-value {
  font-size: 30rpx;
  font-weight: bold;
  color: #667eea;
}

.score-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-top: 2rpx solid #667eea;
  margin-top: 20rpx;
}

.total-label {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
}

.questions-summary {
  margin-bottom: 40rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.question-summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.summary-question {
  flex: 1;
  display: flex;
}

.summary-number {
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
  margin-right: 10rpx;
  min-width: 40rpx;
}

.summary-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

.summary-score {
  font-size: 26rpx;
  font-weight: bold;
  color: #52c41a;
  margin-left: 20rpx;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes recording-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

/* 在现有样式基础上添加倒计时样式 */

/* 倒计时显示 */
.countdown-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.countdown-circle {
  width: 160rpx;
  height: 160rpx;
  border: 8rpx solid #667eea;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  margin-bottom: 20rpx;
  animation: countdown-pulse 1s infinite;
}

/* 不同阶段的颜色 */
.countdown-circle.recording {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.countdown-circle.prepare {
  border-color: #f39c12;
  background: rgba(243, 156, 18, 0.1);
}

.countdown-circle.answering {
  border-color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

.countdown-number {
  font-size: 60rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.countdown-circle.recording .countdown-number {
  color: #e74c3c;
}

.countdown-circle.prepare .countdown-number {
  color: #f39c12;
}

.countdown-circle.answering .countdown-number {
  color: #27ae60;
}

.countdown-unit {
  font-size: 24rpx;
  color: #667eea;
  margin-top: 5rpx;
}

.countdown-circle.recording .countdown-unit {
  color: #e74c3c;
}

.countdown-circle.prepare .countdown-unit {
  color: #f39c12;
}

.countdown-circle.answering .countdown-unit {
  color: #27ae60;
}

.countdown-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

@keyframes countdown-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
} 