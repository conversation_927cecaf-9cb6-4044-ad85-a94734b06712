// pages/profile/received/received.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    receivedShares: [], // 收到的分享列表（保留兼容性）
    modeGroups: [], // 按模式分组的分享列表
    loading: true,
    currentUser: null,
    showDetailModal: false,
    currentDetail: null,
    statistics: {
      totalShares: 0,
      totalTests: 0,
      averageScore: 0,
      recentActivity: null
    },
    shareId: '', // 输入的分享ID
    allSelected: false // 是否全选
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getCurrentUser();
    this.loadReceivedShares();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查是否需要刷新数据
    const app = getApp();
    if (app.globalData && app.globalData.needRefreshReceivedShares) {
      console.log('检测到需要刷新收到的分享数据');
      app.globalData.needRefreshReceivedShares = false;
      this.loadReceivedShares();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReceivedShares();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    const userInfo = wx.getStorageSync('userInfo') || {};

    // 如果用户信息不完整，尝试从其他地方获取
    if (!userInfo.nickName && !userInfo.name) {
      userInfo.nickName = '我';
    }

    console.log('当前用户信息:', userInfo);

    this.setData({
      currentUser: userInfo
    });
  },

  /**
   * 加载收到的分享记录
   * @param {boolean} forceRefresh - 是否强制刷新，清除缓存
   */
  async loadReceivedShares(forceRefresh = false) {
    if (forceRefresh) {
      console.log('=== 强制刷新收到的分享数据 ===');
    }
    try {
      this.setData({ loading: true });
      
      const currentUser = this.data.currentUser;
      if (!currentUser || !currentUser.openid) {
        this.setData({
          loading: false,
          receivedShares: []
        });
        return;
      }



      // 首先尝试从云端获取参与的分享测试
      let cloudShares = [];
      try {
        const result = await wx.cloud.callFunction({
          name: 'getMyShares',
          data: {
            type: 'participated'
          }
        });

        if (result.result.success) {
          cloudShares = result.result.data;
          console.log('从云端加载参与的分享测试:', cloudShares.length);
        }
      } catch (cloudError) {
        console.log('从云端加载分享测试失败，使用本地数据:', cloudError);
      }

      // 同时从本地存储获取分享测试（兼容旧数据）
      const shareTests = wx.getStorageSync('shareTests') || {};
      const localShares = [];

      // 遍历所有分享测试，找到当前用户参与的
      Object.keys(shareTests).forEach(shareId => {
        const shareData = shareTests[shareId];
        
        // 检查当前用户是否访问过这个分享
        if (shareData.visitors) {
          const visitor = shareData.visitors.find(v => v.openid === currentUser.openid);
          if (visitor) {
            // 获取当前用户在这个分享中的测试记录
            const userResults = shareData.results ? 
              shareData.results.filter(r => r.participantOpenid === currentUser.openid) : [];

            localShares.push({
              shareId: shareId,
              shareData: shareData,
              visitor: visitor,
              userResults: userResults,
              shareTitle: this.generateShareTitle(shareData),
              testMode: shareData.testMode || shareData.testType,
              creatorInfo: shareData.creatorInfo,
              createTime: shareData.createTime,
              lastTestTime: visitor.lastTestTime,
              testCount: visitor.testCount || 0,
              bestScore: visitor.bestScore || 0,
              averageScore: visitor.averageScore || 0,
              latestTime: visitor.latestTime || null, // 消消乐模式的用时
              isExpired: this.checkIfExpired(shareData),
              isMultiLevel: this.checkIsMultiLevel(shareData),
              totalLevels: this.getTotalLevels(shareData),
              myProgress: shareData.levelProgress ? shareData.levelProgress[currentUser.openid] : null
            });
          }
        }
      });

      // 获取已删除的分享ID列表
      const deletedShares = wx.getStorageSync('deletedReceivedShares') || [];
      const deletedShareIds = new Set(deletedShares);

      // 合并云端和本地数据，去重，并过滤已删除的项目
      // 首先处理云端数据，确保字段名一致
      const processedCloudShares = cloudShares
        .filter(share => !deletedShareIds.has(share.shareId)) // 过滤已删除的项目
        .map(share => ({
          ...share,
          testMode: share.testType || share.testMode, // 统一字段名
          shareTitle: this.generateShareTitle(share)
        }));

      const allShares = [...processedCloudShares];
      const cloudShareIds = new Set(processedCloudShares.map(share => share.shareId));

      localShares.forEach(localShare => {
        if (!cloudShareIds.has(localShare.shareId) && !deletedShareIds.has(localShare.shareId)) {
          allShares.push({
            ...localShare,
            myInfo: {
              firstVisitTime: localShare.visitor?.firstVisitTime,
              lastTestTime: localShare.lastTestTime,
              testCount: localShare.testCount,
              bestScore: localShare.bestScore,
              latestScore: localShare.visitor?.latestScore || 0,
              latestAccuracy: localShare.visitor?.latestAccuracy || 0,
              results: localShare.userResults,
              progress: localShare.myProgress
            }
          });
        }
      });

      // 按最后测试时间排序
      allShares.sort((a, b) => {
        const timeA = a.myInfo?.lastTestTime || a.lastTestTime || a.createTime || 0;
        const timeB = b.myInfo?.lastTestTime || b.lastTestTime || b.createTime || 0;
        return new Date(timeB) - new Date(timeA);
      });

              // 为每个分享添加选中状态（用于批量删除）
        const receivedShares = allShares.map((share, index) => ({
          ...share,
          index: index,
                    isSelected: false, // 选中状态
          // 格式化时间显示
          firstVisitTimeText: this.formatTime(share.myInfo?.firstVisitTime || share.firstVisitTime),
          lastTestTimeText: this.formatTime(share.myInfo?.lastTestTime || share.lastTestTime),
          createTimeText: this.formatTime(share.createTime),
          // 分享人显示名称处理
          displayCreatorName: this.getDisplayCreatorName(share),
          // 多关卡进度显示
          levelProgressText: this.getLevelProgressText(share),
        shareTitle: share.shareTitle || this.generateShareTitle(share),
        isExpired: share.isExpired || this.checkIfExpired(share)
      }));

      // 计算统计数据
      const statistics = this.calculateStatistics(receivedShares);

      // 自动清理过期任务
      this.autoCleanExpiredShares(receivedShares);

      // 按模式分组
      const modeGroups = this.createModeGroups(receivedShares);

      // 强制修复图标显示问题 - 使用真实国旗emoji
      modeGroups.forEach(group => {
        if (group.mode === 'translate') {
          group.icon = '🇨🇳'; // 英译汉 - 中国五星红旗
          console.log('设置英译汉图标为中国国旗:', group.icon);
        } else if (group.mode === 'chinese') {
          group.icon = '🇺🇸'; // 汉译英 - 美国星条旗
          console.log('设置汉译英图标为美国国旗:', group.icon);
        }
      });

      console.log('最终的模式分组数据:', modeGroups); // 调试信息

      this.setData({
        receivedShares: receivedShares, // 保留兼容性
        modeGroups: modeGroups, // 新的分组数据
        statistics: statistics,
        loading: false,
        allSelected: false // 初始化全选状态
      });

    } catch (error) {
      console.error('加载收到的分享失败:', error);
      this.setData({ 
        loading: false,
        receivedShares: [] 
      });
    }
  },

  /**
   * 自动清理过期任务
   */
  async autoCleanExpiredShares(receivedShares) {
    try {
      const expiredShares = receivedShares.filter(share => share.isExpired);

      if (expiredShares.length === 0) {
        return;
      }

      console.log(`发现${expiredShares.length}个过期分享，开始自动清理...`);

      // 清理云端过期数据
      const expiredShareIds = expiredShares.map(share => share.shareId);

      try {
        await wx.cloud.callFunction({
          name: 'deleteShareTest',
          data: {
            shareIds: expiredShareIds,
            type: 'participant'
          }
        });
        console.log('云端过期数据清理完成');
      } catch (cloudError) {
        console.log('云端过期数据清理失败:', cloudError);
      }

      // 清理本地过期数据
      this.cleanLocalExpiredShares(expiredShareIds);

    } catch (error) {
      console.error('自动清理过期任务失败:', error);
    }
  },

  /**
   * 清理本地过期分享数据
   */
  cleanLocalExpiredShares(expiredShareIds) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      const currentUser = this.data.currentUser;

      expiredShareIds.forEach(shareId => {
        const shareData = shareTests[shareId];
        if (shareData) {
          // 从访问者列表中移除当前用户
          if (shareData.visitors) {
            shareData.visitors = shareData.visitors.filter(v => v.openid !== currentUser.openid);
          }

          // 从测试结果中移除当前用户的记录
          if (shareData.results) {
            shareData.results = shareData.results.filter(r => r.participantOpenid !== currentUser.openid);
          }

          // 从关卡进度中移除当前用户
          if (shareData.levelProgress) {
            delete shareData.levelProgress[currentUser.openid];
          }

          // 如果没有其他参与者，删除整个分享记录
          if ((!shareData.visitors || shareData.visitors.length === 0) &&
              (!shareData.results || shareData.results.length === 0)) {
            delete shareTests[shareId];
          } else {
            shareTests[shareId] = shareData;
          }
        }
      });

      wx.setStorageSync('shareTests', shareTests);
      console.log('本地过期数据清理完成');

    } catch (error) {
      console.error('清理本地过期数据失败:', error);
    }
  },

  /**
   * 生成分享标题
   */
  generateShareTitle(shareData) {
    const testModeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写测试',
      'custom': '消消乐',
      'elimination': '消消乐'
    };
    
    const mode = testModeMap[shareData.testMode || shareData.testType] || '测试';
    const library = shareData.libraryName || '词库';
    
    return `${mode} - ${library}`;
  },

  /**
   * 检查分享是否过期
   */
  checkIfExpired(shareData) {
    if (!shareData.expireTime) {
      return false;
    }

    const now = new Date();
    const expireTime = new Date(shareData.expireTime);
    return now > expireTime;
  },

  /**
   * 检查是否为多关卡任务
   */
  checkIsMultiLevel(shareData) {
    // 如果明确设置了isMultiLevel，直接返回
    if (shareData.isMultiLevel !== undefined) {
      return shareData.isMultiLevel;
    }

    // 对于消消乐，检查是否有多个组
    if (shareData.testMode === 'elimination' || shareData.testType === 'elimination') {
      const wordsPerGroup = shareData.wordsPerGroup || 10;
      const totalWords = shareData.allWords ? shareData.allWords.length : (shareData.words ? shareData.words.length : 0);
      const totalGroups = Math.ceil(totalWords / wordsPerGroup);
      console.log('消消乐多关卡检查:', { totalWords, wordsPerGroup, totalGroups, isMulti: totalGroups > 1 });
      return totalGroups > 1;
    }

    // 对于听写模式，检查是否有多个关卡
    if (shareData.testMode === 'dictation' || shareData.testType === 'dictation') {
      // 检查是否有levels数组或totalLevels大于1
      const hasLevels = shareData.levels && shareData.levels.length > 1;
      const hasTotalLevels = (shareData.totalLevels || 1) > 1;
      console.log('听写多关卡检查:', { hasLevels, hasTotalLevels, totalLevels: shareData.totalLevels });
      return hasLevels || hasTotalLevels;
    }

    // 其他类型检查totalLevels
    const isMulti = (shareData.totalLevels || 1) > 1;
    console.log('其他模式多关卡检查:', { testMode: shareData.testMode, totalLevels: shareData.totalLevels, isMulti });
    return isMulti;
  },

  /**
   * 获取总关卡数
   */
  getTotalLevels(shareData) {
    // 对于消消乐，计算总组数
    if (shareData.testMode === 'elimination' || shareData.testType === 'elimination') {
      const wordsPerGroup = shareData.wordsPerGroup || 10;
      const totalWords = shareData.allWords ? shareData.allWords.length : (shareData.words ? shareData.words.length : 0);
      return Math.ceil(totalWords / wordsPerGroup);
    }

    // 对于听写模式，优先使用levels数组长度
    if (shareData.testMode === 'dictation' || shareData.testType === 'dictation') {
      if (shareData.levels && shareData.levels.length > 0) {
        return shareData.levels.length;
      }
    }

    // 其他类型直接返回totalLevels
    return shareData.totalLevels || 1;
  },

  /**
   * 计算统计数据
   */
  calculateStatistics(receivedShares) {
    if (!receivedShares || receivedShares.length === 0) {
      return {
        totalShares: 0,
        totalTests: 0,
        averageScore: 0,
        recentActivity: null
      };
    }

    const totalShares = receivedShares.length;

    // 计算总测试次数
    const totalTests = receivedShares.reduce((sum, share) => {
      const testCount = (share.myInfo && share.myInfo.testCount) || 0;
      return sum + testCount;
    }, 0);

    // 计算平均分
    let totalScore = 0;
    let validScoreCount = 0;

    receivedShares.forEach(share => {
      if (share.myInfo && share.myInfo.results && share.myInfo.results.length > 0) {
        share.myInfo.results.forEach(result => {
          if (result.score !== undefined && result.score !== null) {
            totalScore += result.score;
            validScoreCount++;
          }
        });
      }
    });

    const averageScore = validScoreCount > 0 ? Math.round(totalScore / validScoreCount) : 0;

    // 最近活动时间
    let recentActivity = null;
    receivedShares.forEach(share => {
      const lastTime = (share.myInfo && share.myInfo.lastTestTime) || share.lastTestTime;
      if (lastTime && (!recentActivity || new Date(lastTime) > new Date(recentActivity))) {
        recentActivity = lastTime;
      }
    });

    return {
      totalShares,
      totalTests,
      averageScore,
      recentActivity
    };
  },

  /**
   * 查看分享详情
   */
  viewShareDetail(e) {
    const { mode, index } = e.currentTarget.dataset;

    // 从分组数据中找到对应的分享
    let share = null;
    if (mode && index !== undefined) {
      // 新的分组模式
      const modeGroup = this.data.modeGroups.find(group => group.mode === mode);
      if (modeGroup && modeGroup.shares[index]) {
        share = modeGroup.shares[index];
      }
    } else {
      // 兼容旧的方式
      const shareIndex = e.currentTarget.dataset.index;
      share = this.data.receivedShares[shareIndex];
    }

    if (!share) {
      wx.showToast({
        title: '找不到分享数据',
        icon: 'none'
      });
      return;
    }

    // 计算平均分（避免在WXML中使用复杂表达式）
    let averageScore = 0;
    if (share.myInfo && share.myInfo.results && share.myInfo.results.length > 0) {
      const totalScore = share.myInfo.results.reduce((sum, result) => sum + result.score, 0);
      averageScore = Math.round(totalScore / share.myInfo.results.length);
    }

    // 处理关卡数据和正确率
    let levelDataWithAccuracy = [];
    if (share.isMultiLevel && share.myInfo && share.myInfo.progress) {
      const testMode = share.testMode || share.testType;
      console.log('完整的share.myInfo:', share.myInfo);
      levelDataWithAccuracy = this.processLevelDataWithAccuracy(share.myInfo.progress, testMode, share.myInfo.results);
    }

    // 为详情弹窗准备数据
    const detailData = {
      ...share,
      averageScore: averageScore,
      levelDataWithAccuracy: levelDataWithAccuracy
    };

    this.setData({
      currentDetail: detailData,
      showDetailModal: true
    });
  },

  /**
   * 处理关卡数据和正确率
   */
  processLevelDataWithAccuracy(progress, testMode, allResults) {
    console.log('处理关卡数据，progress:', progress);
    console.log('testMode:', testMode);
    console.log('allResults:', allResults);

    const levelDataWithAccuracy = [];

    // 确保scores是数组
    let scores = progress.scores || [];
    if (!Array.isArray(scores)) {
      console.warn('scores不是数组，尝试转换:', scores);
      // 如果scores是对象，尝试转换为数组
      if (typeof scores === 'object') {
        scores = Object.values(scores);
      } else {
        scores = [];
      }
    }

    // 优先使用传入的allResults，如果没有则使用progress.results
    const results = allResults || progress.results || [];
    console.log('scores:', scores);
    console.log('使用的results:', results);

    // 详细查看results数组的结构
    if (results.length > 0) {
      console.log('第一个result的字段:', Object.keys(results[0]));
      console.log('第一个result的完整内容:', results[0]);

      // 查看所有results的levelIndex字段
      results.forEach((result, idx) => {
        console.log(`Result ${idx}:`, {
          levelIndex: result.levelIndex,
          level: result.level,
          groupIndex: result.groupIndex,
          score: result.score,
          accuracy: result.accuracy,
          correctCount: result.correctCount,
          totalCount: result.totalCount
        });
      });
    }

    const isEliminationMode = testMode === 'elimination' || testMode === 'puzzle';

    // 如果没有分数数据，尝试从results中构建（保留最高分）
    if (scores.length === 0 && results.length > 0) {
      const levelScores = {};
      const levelBestResults = {}; // 保存每关最高分对应的结果

      results.forEach(result => {
        const levelIndex = result.levelIndex || 0;
        const currentScore = result.score || 0;

        if (!levelScores[levelIndex] || currentScore > levelScores[levelIndex]) {
          levelScores[levelIndex] = currentScore;
          levelBestResults[levelIndex] = result; // 保存最高分对应的结果
        }
      });

      scores = Object.keys(levelScores).map(key => levelScores[key]);

      // 将最高分对应的结果保存到全局，供后续正确率计算使用
      this.levelBestResults = levelBestResults;
    }

    // 遍历分数数组
    scores.forEach((score, index) => {
      let accuracy = 0;

      // 消消乐模式不计算正确率，因为必须完成全部匹配才能过关
      if (!isEliminationMode) {
        // 优先使用最高分对应的结果计算正确率
        if (this.levelBestResults && this.levelBestResults[index]) {
          const bestResult = this.levelBestResults[index];
          console.log(`第${index + 1}关使用最高分结果:`, bestResult);

          if (bestResult.accuracy !== undefined && bestResult.accuracy !== null) {
            const accuracyValue = typeof bestResult.accuracy === 'string' ?
              parseFloat(bestResult.accuracy) : bestResult.accuracy;
            accuracy = accuracyValue;
            console.log(`第${index + 1}关最高分正确率: ${accuracy}%`);
          } else if (bestResult.correctCount !== undefined && bestResult.totalQuestions !== undefined && bestResult.totalQuestions > 0) {
            accuracy = Math.round((bestResult.correctCount / bestResult.totalQuestions) * 100);
            console.log(`第${index + 1}关最高分计算正确率: ${bestResult.correctCount}/${bestResult.totalQuestions} = ${accuracy}%`);
          } else if (bestResult.correctCount !== undefined && bestResult.totalCount !== undefined && bestResult.totalCount > 0) {
            accuracy = Math.round((bestResult.correctCount / bestResult.totalCount) * 100);
            console.log(`第${index + 1}关最高分计算正确率: ${bestResult.correctCount}/${bestResult.totalCount} = ${accuracy}%`);
          } else if (bestResult.accuracyRate !== undefined && bestResult.accuracyRate !== null) {
            accuracy = bestResult.accuracyRate;
            console.log(`第${index + 1}关最高分使用accuracyRate: ${accuracy}%`);
          }
        } else {
          // 如果没有最高分结果，回退到原有逻辑
          const levelId = (index + 1).toString();
          const levelResults = results.filter(result =>
            result.levelId === levelId ||
            result.levelId === (index + 1) ||
            result.levelIndex === index
          );
          console.log(`第${index + 1}关(levelId: ${levelId})的测试结果:`, levelResults);

          if (levelResults.length > 0) {
            // 找到最高分的结果
            let bestResult = levelResults[0];
            for (const result of levelResults) {
              if ((result.score || 0) > (bestResult.score || 0)) {
                bestResult = result;
              }
            }

            console.log(`第${index + 1}关最高分结果:`, bestResult);

            if (bestResult.accuracy !== undefined && bestResult.accuracy !== null) {
              const accuracyValue = typeof bestResult.accuracy === 'string' ?
                parseFloat(bestResult.accuracy) : bestResult.accuracy;
              accuracy = accuracyValue;
            } else if (bestResult.correctCount !== undefined && bestResult.totalQuestions !== undefined && bestResult.totalQuestions > 0) {
              accuracy = Math.round((bestResult.correctCount / bestResult.totalQuestions) * 100);
            } else if (bestResult.correctCount !== undefined && bestResult.totalCount !== undefined && bestResult.totalCount > 0) {
              accuracy = Math.round((bestResult.correctCount / bestResult.totalCount) * 100);
            } else if (bestResult.accuracyRate !== undefined && bestResult.accuracyRate !== null) {
              accuracy = bestResult.accuracyRate;
            } else {
              accuracy = null;
              console.log(`第${index + 1}关无正确率数据`);
            }
          } else {
            accuracy = null;
            console.log(`第${index + 1}关没有找到测试结果`);
          }
        }
      }

      const levelData = {
        levelIndex: index,
        score: score || 0,
        accuracy: isEliminationMode ? null : accuracy, // 消消乐模式不显示正确率
        isEliminationMode: isEliminationMode
      };

      console.log(`第${index + 1}关数据:`, levelData);
      levelDataWithAccuracy.push(levelData);
    });

    console.log('最终关卡数据:', levelDataWithAccuracy);
    return levelDataWithAccuracy;
  },

  /**
   * 关闭详情弹窗
   */
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      currentDetail: null
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  },

  /**
   * 重新参加测试
   */
  retakeTest(e) {
    const { mode, index } = e.currentTarget.dataset;

    // 从分组数据中找到对应的分享
    let share = null;
    if (mode && index !== undefined) {
      // 新的分组模式
      const modeGroup = this.data.modeGroups.find(group => group.mode === mode);
      if (modeGroup && modeGroup.shares[index]) {
        share = modeGroup.shares[index];
      }
    } else {
      // 兼容旧的方式
      const shareIndex = e.currentTarget.dataset.index;
      share = this.data.receivedShares[shareIndex];
    }

    if (!share) {
      wx.showToast({
        title: '找不到分享数据',
        icon: 'none'
      });
      return;
    }

    // 统一处理测试类型字段名
    const testMode = share.testType || share.testMode;

    // 根据测试类型跳转到对应页面
    let url = '';
    switch (testMode) {
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${share.shareId}&shareMode=share&testMode=${testMode}`;
        break;
      case 'dictation':
      case 'spelling':
        url = `/pages/spelling/practice/practice?shareId=${share.shareId}&shareMode=share`;
        break;
      case 'phrase_en2zh':
        url = `/pages/wordtest/test/test?shareId=${share.shareId}&shareMode=share&testMode=en_to_cn&isPhrase=true`;
        break;
      case 'phrase_zh2en':
        url = `/pages/wordtest/test/test?shareId=${share.shareId}&shareMode=share&testMode=cn_to_en&isPhrase=true`;
        break;
      case 'elimination':
      case 'puzzle':
      case 'custom':
        url = `/pages/task/puzzle/puzzle?shareId=${share.shareId}&isShared=true&mode=custom`;
        break;
      default:
        wx.showToast({
          title: `未知的测试类型: ${testMode}`,
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 选择关卡开始（多关卡任务）
   */
  selectLevelToStart(e) {
    const { mode, index } = e.currentTarget.dataset;

    // 从分组数据中找到对应的分享
    let share = null;
    if (mode && index !== undefined) {
      // 新的分组模式
      const modeGroup = this.data.modeGroups.find(group => group.mode === mode);
      if (modeGroup && modeGroup.shares[index]) {
        share = modeGroup.shares[index];
      }
    } else {
      // 兼容旧的方式
      const shareIndex = e.currentTarget.dataset.index;
      share = this.data.receivedShares[shareIndex];
    }

    if (!share) {
      wx.showToast({
        title: '找不到分享数据',
        icon: 'none'
      });
      return;
    }

    // 跳转到关卡选择界面，类似单词竞赛的关卡选择
    wx.navigateTo({
      url: `/pages/competition/level-select/level-select?shareId=${share.shareId}&mode=share&testType=${share.testType || share.testMode}`
    });
  },

  /**
   * 开始单组测试
   */
  startSingleTest(e) {
    const { mode, index } = e.currentTarget.dataset;

    // 从分组数据中找到对应的分享
    let share = null;
    if (mode && index !== undefined) {
      // 新的分组模式
      const modeGroup = this.data.modeGroups.find(group => group.mode === mode);
      if (modeGroup && modeGroup.shares[index]) {
        share = modeGroup.shares[index];
      }
    } else {
      // 兼容旧的方式
      const shareIndex = e.currentTarget.dataset.index;
      share = this.data.receivedShares[shareIndex];
    }

    if (!share) {
      wx.showToast({
        title: '找不到分享数据',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到测试，单组任务不需要关卡ID
    this.navigateToTest(share);
  },



  /**
   * 删除分享记录
   */
  deleteShare(e) {
    const index = e.currentTarget.dataset.index;
    const share = this.data.receivedShares[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除分享"${share.shareTitle}"吗？\n删除后将无法再查看此分享的测试记录。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteShare(share.shareId);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async doDeleteShare(shareId) {
    console.log('=== 开始删除收到的分享 ===');
    console.log('要删除的shareId:', shareId);
    console.log('当前用户:', this.data.currentUser?.openid);

    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 优先从云端删除参与记录
      let cloudDeleteSuccess = false;
      try {
        const result = await wx.cloud.callFunction({
          name: 'deleteShareTest',
          data: {
            shareIds: [shareId],
            type: 'participant' // 参与者删除
          }
        });

        console.log('云函数删除结果:', result.result);

        if (result.result.success) {
          cloudDeleteSuccess = true;
          console.log('云端删除成功，删除数量:', result.result.data?.deleteCount);
        } else {
          console.log('云端删除失败:', result.result.message);
        }
      } catch (cloudError) {
        console.log('从云端删除参与记录失败:', cloudError);
      }

      // 同时从本地存储中删除（兼容旧数据）
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareData = shareTests[shareId];
      let localDeleteSuccess = false;

      if (shareData && shareData.visitors) {
        // 从访问者列表中移除当前用户
        shareData.visitors = shareData.visitors.filter(v => v.openid !== this.data.currentUser.openid);

        // 从测试结果中移除当前用户的记录
        if (shareData.results) {
          shareData.results = shareData.results.filter(r => r.participantOpenid !== this.data.currentUser.openid);
        }

        shareTests[shareId] = shareData;
        wx.setStorageSync('shareTests', shareTests);
        localDeleteSuccess = true;
      }

      wx.hideLoading();

      console.log('删除结果 - 云端成功:', cloudDeleteSuccess, '本地成功:', localDeleteSuccess);

      if (cloudDeleteSuccess || localDeleteSuccess) {
        console.log('删除成功，开始刷新数据');

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 立即从当前显示数据中移除已删除的项目
        const updatedShares = this.data.receivedShares.filter(share => share.shareId !== shareId);
        this.setData({
          receivedShares: updatedShares,
          allSelected: false
        });

        // 记录已删除的项目，避免重新加载时恢复
        const deletedShares = wx.getStorageSync('deletedReceivedShares') || [];
        if (!deletedShares.includes(shareId)) {
          deletedShares.push(shareId);
          wx.setStorageSync('deletedReceivedShares', deletedShares);
        }

        // 延迟重新加载数据，确保删除操作完成
        setTimeout(async () => {
          await this.loadReceivedShares(true);
        }, 500);

        console.log('删除后刷新完成');
      } else {
        console.log('删除失败：云端和本地都没有成功删除');
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('删除分享失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },



  /**
   * 头像加载错误处理
   */
  onAvatarError(e) {
    const index = e.currentTarget.dataset.index;
    console.log('头像加载失败，索引:', index, '错误信息:', e.detail);

    // 使用默认头像
    const receivedShares = this.data.receivedShares;
    if (receivedShares[index]) {
      receivedShares[index].creatorInfo.avatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
      this.setData({
        receivedShares: receivedShares
      });
    }
  },

  /**
   * 获取分享人显示名称
   */
  getDisplayCreatorName(share) {
    // 直接使用creatorInfo中的昵称
    if (share.creatorInfo && share.creatorInfo.nickName) {
      return share.creatorInfo.nickName;
    }

    // 兜底显示
    return '微信用户';
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // 今天
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  },

  /**
   * 格式化测试模式
   */
  getTestModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写测试',
      'custom': '消消乐'
    };
    return modeMap[mode] || '未知模式';
  },



  /**
   * 输入分享ID
   */
  onShareIdInput(e) {
    this.setData({
      shareId: e.detail.value.trim()
    });
  },

  /**
   * 参与分享测试
   */
  async joinShareTest() {
    const { shareId } = this.data;
    
    if (!shareId) {
      wx.showToast({
        title: '请输入测试ID',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 首先尝试从云端获取分享测试
      let shareTestData = null;
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: {
            shareId: shareId
          }
        });
        
        if (result.result.success) {
          shareTestData = result.result.data;
          console.log('从云端获取分享测试成功:', shareTestData);
        } else {
          throw new Error(result.result.message || '获取分享测试失败');
        }
      } catch (cloudError) {
        console.log('从云端获取分享测试失败，尝试本地存储:', cloudError);
        
        // 如果云端获取失败，尝试本地存储
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareTestData = shareTests[shareId];
        
        if (shareTestData) {
          console.log('从本地存储获取分享测试成功');
        }
      }

      this.setData({ loading: false });

      if (shareTestData) {
        // 检查是否过期
        if (this.checkIfExpired(shareTestData)) {
          wx.showModal({
            title: '测试已过期',
            content: '该分享测试已过期，无法参与。',
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }
        
        this.navigateToTest(shareTestData);
      } else {
        wx.showModal({
          title: '测试不存在',
          content: '找不到该测试ID对应的分享测试。\n\n请确认：\n1. 测试ID输入正确\n2. 测试未过期\n3. 分享链接有效',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('参与分享测试失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到对应的测试页面
   */
  navigateToTest(shareTestData) {
    const { shareId } = shareTestData;

    // 统一处理测试类型字段名（云端数据使用testType，本地数据可能使用testMode）
    const testMode = shareTestData.testType || shareTestData.testMode;

    console.log('navigateToTest - 分享测试数据:', {
      shareId,
      testMode,
      testType: shareTestData.testType,
      isMultiLevel: shareTestData.isMultiLevel
    });

    if (!testMode) {
      console.error('测试类型未定义:', shareTestData);
      wx.showToast({
        title: '测试类型未定义',
        icon: 'none'
      });
      return;
    }

    // 获取当前用户信息
    const currentUser = wx.getStorageSync('userInfo') || {};
    const currentUserOpenid = currentUser.openid;

    // 检查用户进度，确定应该进入哪个关卡
    let levelId = 1; // 默认第一关

    if (shareTestData.isMultiLevel && currentUserOpenid) {
      // 获取用户的关卡进度
      const userProgress = shareTestData.levelProgress && shareTestData.levelProgress[currentUserOpenid];
      if (userProgress && userProgress.currentLevel) {
        levelId = userProgress.currentLevel;
        console.log('检测到用户进度，继续关卡:', levelId);
      } else {
        console.log('用户首次参与多关卡测试，从第1关开始');
      }
    }

    let url = '';

    switch (testMode) {
      case 'dictation':
      case 'spelling':
        url = `/pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
        if (shareTestData.isMultiLevel) {
          url += `&levelId=${levelId}`;
        }
        break;
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
        if (shareTestData.isMultiLevel) {
          url += `&levelId=${levelId}`;
        }
        break;
      case 'phrase_en2zh':
        // 短语英译汉
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=en_to_cn&isPhrase=true`;
        if (shareTestData.isMultiLevel) {
          url += `&levelId=${levelId}`;
        }
        break;
      case 'phrase_zh2en':
        // 短语汉译英
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=cn_to_en&isPhrase=true`;
        if (shareTestData.isMultiLevel) {
          url += `&levelId=${levelId}`;
        }
        break;
      case 'elimination':
      case 'puzzle':
        // 消消乐需要传递游戏模式参数，避免显示模式选择界面
        const gameMode = shareTestData.settings?.eliminationGameMode || shareTestData.eliminationGameMode || 'time';
        url = `/pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom&gameMode=${gameMode}`;
        if (shareTestData.isMultiLevel || this.checkIsMultiLevel(shareTestData)) {
          url += `&levelId=${levelId}`;
        }
        break;
      case 'custom':
        url = `/pages/task/custom/custom?shareId=${shareId}&shareMode=share`;
        if (shareTestData.isMultiLevel) {
          url += `&levelId=${levelId}`;
        }
        break;
      default:
        console.error('不支持的测试类型:', testMode);
        wx.showToast({
          title: `不支持的测试类型: ${testMode}`,
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到分享测试:', url);
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },



  /**
   * 清空输入
   */
  clearInput() {
    this.setData({ shareId: '' });
  },

  /**
   * 扫码参与（预留功能）
   */

  /**
   * 获取关卡进度文本
   */
  getLevelProgressText(share) {
    if (!share.isMultiLevel) {
      return null;
    }
    
    const progress = share.myInfo?.progress || share.myProgress;
    if (progress) {
      const completed = progress.completedLevels ? progress.completedLevels.length : 0;
      const total = share.totalLevels || 1;
      const current = progress.currentLevel || 1;
      
      return `第${current}关 | 完成${completed}/${total}关`;
    }
    
    return `第1关 | 完成0/${share.totalLevels || 1}关`;
  },

  /**
   * 选择/取消选择分享
   */
  toggleSelectShare(e) {
    const { mode, index } = e.currentTarget.dataset;

    // 更新分组数据中的选择状态
    const modeGroups = [...this.data.modeGroups];
    const modeGroup = modeGroups.find(group => group.mode === mode);

    if (modeGroup && modeGroup.shares[index]) {
      modeGroup.shares[index].isSelected = !modeGroup.shares[index].isSelected;
    }

    // 同时更新兼容性数据
    const receivedShares = [...this.data.receivedShares];
    const shareId = modeGroup.shares[index].shareId;
    const shareIndex = receivedShares.findIndex(share => share.shareId === shareId);
    if (shareIndex !== -1) {
      receivedShares[shareIndex].isSelected = modeGroup.shares[index].isSelected;
    }

    // 检查是否全选
    const allShares = modeGroups.flatMap(group => group.shares);
    const allSelected = allShares.every(share => share.isSelected);

    this.setData({
      modeGroups: modeGroups,
      receivedShares: receivedShares, // 保持兼容性
      allSelected: allSelected
    });
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const currentAllSelected = this.data.allSelected;
    const newSelectedState = !currentAllSelected;

    // 更新分组数据
    const modeGroups = this.data.modeGroups.map(group => ({
      ...group,
      shares: group.shares.map(share => ({
        ...share,
        isSelected: newSelectedState
      }))
    }));

    // 更新兼容性数据
    const receivedShares = this.data.receivedShares.map(share => ({
      ...share,
      isSelected: newSelectedState
    }));

    this.setData({
      modeGroups: modeGroups,
      receivedShares: receivedShares, // 保持兼容性
      allSelected: newSelectedState
    });
  },

  /**
   * 批量删除选中的分享
   */
  batchDeleteShares() {
    // 从分组数据中获取选中的分享
    const allShares = this.data.modeGroups.flatMap(group => group.shares);
    const selectedShares = allShares.filter(share => share.isSelected);

    if (selectedShares.length === 0) {
      wx.showToast({
        title: '请选择要删除的分享',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedShares.length}个分享吗？`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doBatchDelete(selectedShares);
        }
      }
    });
  },

  /**
   * 获取模式配置
   */
  getModeConfig() {
    return {
      'translate': {
        name: '英译汉',
        icon: '🇨🇳',
        validModes: ['translate', 'english-chinese', 'en_to_cn', 'phrase_en2zh']
      },
      'chinese': {
        name: '汉译英',
        icon: '🇺🇸',
        validModes: ['chinese', 'chinese-english', 'cn_to_en', 'phrase_zh2en']
      },
      'dictation': {
        name: '听写测试',
        icon: '🎧',
        validModes: ['dictation', 'spelling']
      },
      'puzzle': {
        name: '消消乐',
        icon: '🧩',
        validModes: ['puzzle', 'elimination', 'custom']
      }
    };
  },

  /**
   * 标准化测试模式
   */
  normalizeTestMode(testMode) {
    if (!testMode) return 'translate'; // 默认为英译汉

    const configs = this.getModeConfig();

    // 直接匹配
    if (configs[testMode]) {
      return testMode;
    }

    // 查找别名匹配
    for (const [mode, config] of Object.entries(configs)) {
      if (config.validModes.includes(testMode)) {
        return mode;
      }
    }

    // 兼容旧的模式名称
    const lowerMode = testMode.toLowerCase();
    if (lowerMode.includes('phrase_en2zh') || lowerMode.includes('phrase') && lowerMode.includes('en')) {
      return 'translate';
    }
    if (lowerMode.includes('phrase_zh2en') || lowerMode.includes('phrase') && lowerMode.includes('zh')) {
      return 'chinese';
    }
    if (lowerMode.includes('en_to_cn') || lowerMode.includes('english') || lowerMode.includes('translate')) {
      return 'translate';
    }
    if (lowerMode.includes('cn_to_en') || lowerMode.includes('chinese')) {
      return 'chinese';
    }
    if (lowerMode.includes('dictation') || lowerMode.includes('听写') || lowerMode.includes('spell')) {
      return 'dictation';
    }
    if (lowerMode.includes('puzzle') || lowerMode.includes('消消乐') || lowerMode.includes('elimination') || lowerMode.includes('custom')) {
      return 'puzzle';
    }

    // 默认返回英译汉
    return 'translate';
  },

  /**
   * 创建模式分组
   */
  createModeGroups(receivedShares) {
    const configs = this.getModeConfig();
    console.log('模式配置:', configs); // 调试信息
    const groups = {};

    // 初始化所有模式分组
    Object.keys(configs).forEach(mode => {
      let icon = configs[mode].icon;

      groups[mode] = {
        mode,
        name: configs[mode].name,
        icon: icon,
        shares: [],
        expanded: true, // 默认展开
        stats: {
          totalShares: 0,
          totalTests: 0,
          averageScore: 0
        }
      };
      console.log(`初始化模式分组 ${mode}:`, groups[mode]); // 调试信息
    });

    // 将分享按模式分组
    receivedShares.forEach(share => {
      // 统一处理测试类型字段名
      const testMode = share.testType || share.testMode;
      const normalizedMode = this.normalizeTestMode(testMode);
      if (groups[normalizedMode]) {
        groups[normalizedMode].shares.push(share);
      }
    });

    // 计算每个分组的统计数据
    Object.keys(groups).forEach(mode => {
      const group = groups[mode];
      group.stats = this.calculateGroupStats(group.shares);
      group.stats.totalShares = group.shares.length;
    });

    // 转换为数组并过滤掉空分组
    const modeGroups = Object.values(groups)
      .filter(group => group.shares.length > 0)
      .sort((a, b) => b.shares.length - a.shares.length); // 按分享数量排序

    return modeGroups;
  },

  /**
   * 计算分组统计数据
   */
  calculateGroupStats(shares) {
    if (shares.length === 0) {
      return {
        totalShares: 0,
        totalTests: 0,
        averageScore: 0
      };
    }

    let totalTests = 0;
    let totalScore = 0;
    let scoreCount = 0;

    shares.forEach(share => {
      const testCount = share.myInfo?.testCount || 0;
      const bestScore = share.myInfo?.bestScore || 0;

      totalTests += testCount;
      if (bestScore > 0) {
        totalScore += bestScore;
        scoreCount++;
      }
    });

    return {
      totalShares: shares.length,
      totalTests: totalTests,
      averageScore: scoreCount > 0 ? Math.round(totalScore / scoreCount) : 0
    };
  },

  /**
   * 切换模式分组展开/折叠
   */
  toggleModeExpand(e) {
    const mode = e.currentTarget.dataset.mode;
    const modeGroups = [...this.data.modeGroups];

    const targetGroup = modeGroups.find(group => group.mode === mode);
    if (targetGroup) {
      targetGroup.expanded = !targetGroup.expanded;
    }

    this.setData({
      modeGroups: modeGroups
    });
  },

  /**
   * 执行批量删除
   */
  async doBatchDelete(selectedShares) {
    console.log('=== 开始批量删除收到的分享 ===');
    console.log('要删除的分享数量:', selectedShares.length);
    console.log('要删除的shareIds:', selectedShares.map(s => s.shareId));

    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 批量删除：传递shareIds数组
      const shareIds = selectedShares.map(share => share.shareId);

      const deleteResult = await wx.cloud.callFunction({
        name: 'deleteShareTest',
        data: {
          shareIds: shareIds,
          type: 'participant' // 参与者删除
        }
      });

      console.log('批量删除云函数结果:', deleteResult.result);

      wx.hideLoading();

      if (deleteResult.result.success) {
        const deleteCount = deleteResult.result.data.deleteCount;
        console.log('批量删除成功，删除数量:', deleteCount);

        wx.showToast({
          title: `成功删除${deleteCount}个分享`,
          icon: 'success'
        });

        // 立即从当前显示数据中移除已删除的项目
        const deletedShareIds = new Set(shareIds);

        // 更新兼容性数据
        const updatedShares = this.data.receivedShares.filter(share => !deletedShareIds.has(share.shareId));

        // 更新分组数据
        const updatedModeGroups = this.data.modeGroups.map(group => ({
          ...group,
          shares: group.shares.filter(share => !deletedShareIds.has(share.shareId))
        })).filter(group => group.shares.length > 0); // 移除空分组

        this.setData({
          receivedShares: updatedShares,
          modeGroups: updatedModeGroups,
          allSelected: false
        });

        // 记录已删除的项目，避免重新加载时恢复
        const existingDeletedShares = wx.getStorageSync('deletedReceivedShares') || [];
        const newDeletedShares = [...existingDeletedShares, ...shareIds];
        wx.setStorageSync('deletedReceivedShares', [...new Set(newDeletedShares)]);

        // 延迟重新加载数据，确保删除操作完成
        setTimeout(async () => {
          await this.loadReceivedShares(true);
        }, 500);

        console.log('批量删除后刷新完成');
      } else {
        console.log('批量删除失败:', deleteResult.result.message);
        wx.showToast({
          title: deleteResult.result.message || '删除失败，请重试',
          icon: 'error'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 查看分享管理页面
   */
  viewShareManagement(e) {
    const shareId = e.currentTarget.dataset.shareid;
    wx.navigateTo({
      url: `/management/share-management/share-management?shareId=${shareId}`
    });
  },

  /**
   * 复制分享ID
   */
  copyShareId(e) {
    const shareId = e.currentTarget.dataset.shareId;

    if (!shareId) {
      wx.showToast({
        title: '复制失败',
        icon: 'error'
      });
      return;
    }

    wx.setClipboardData({
      data: shareId,
      success: () => {
        wx.showToast({
          title: '测试ID已复制',
          icon: 'success',
          duration: 1500
        });

        // 显示详细说明
        setTimeout(() => {
          wx.showModal({
            title: '测试ID已复制',
            content: `测试ID: ${shareId}\n\n已复制到剪贴板。对方可以在"我的"->"收到的分享"中输入这个ID来参与测试。`,
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  }

})