/* 容器样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
  min-height: 100vh;
  color: white;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 类型选择器 */
.type-selector {
  margin-bottom: 30rpx;
}

.type-tabs {
  white-space: nowrap;
}

.tab-container {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 20rpx;
}

.type-tab {
  display: inline-block;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  white-space: nowrap;
}

.type-tab.active {
  background: white;
  color: #ff6b6b;
  border-color: white;
  font-weight: bold;
}

/* 音标内容 */
.phonetic-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.sounds-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.sound-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.sound-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.sound-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.phonetic-symbol {
  font-size: 48rpx;
  font-weight: bold;
  font-family: 'Times New Roman', serif;
  cursor: pointer;
}

.play-btn {
  font-size: 32rpx;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.play-btn:active {
  opacity: 1;
  transform: scale(1.1);
}

.sound-example {
  font-size: 26rpx;
  margin-bottom: 12rpx;
  opacity: 0.9;
  font-family: 'Times New Roman', serif;
}

.sound-description {
  font-size: 24rpx;
  opacity: 0.8;
  line-height: 1.4;
}

/* 练习按钮 */
.practice-section {
  text-align: center;
}

.practice-btn {
  background: white;
  color: #ff6b6b;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.practice-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 学习提示 */
.tips-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-dot {
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 4rpx;
  opacity: 0.8;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.9;
}

/* 练习模式样式 */
.practice-container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.practice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
}

.practice-title {
  font-size: 36rpx;
  font-weight: bold;
}

.practice-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.9;
}

.exit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.practice-content {
  text-align: center;
}

.question-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
}

.question-title {
  font-size: 32rpx;
  margin-bottom: 30rpx;
  opacity: 0.9;
}

.current-symbol {
  font-size: 80rpx;
  font-weight: bold;
  font-family: 'Times New Roman', serif;
  margin-bottom: 20rpx;
}

.answer-section {
  margin-bottom: 30rpx;
}

.answer-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  width: 100%;
  margin-bottom: 20rpx;
}

.answer-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.practice-hint {
  opacity: 0.7;
  font-size: 24rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .sounds-grid {
    grid-template-columns: 1fr;
  }
  
  .practice-header {
    flex-direction: column;
    gap: 16rpx;
    text-align: center;
  }
} 