<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title-info">
      <view class="title">听后选择</view>
      <view class="progress">第{{currentQuestion}}题 / 共{{totalQuestions}}题</view>
    </view>
    <view class="section-info">
      <text class="section-tag">第{{currentSection}}节</text>
    </view>
  </view>

  <!-- 流程指示器 -->
  <view class="step-indicator">
    <view class="step {{currentStep === 'reading' ? 'active' : currentStep === 'audio' || currentStep === 'answering' || currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">1</text>
      <text class="step-label">阅读题目</text>
    </view>
    <view class="step-line"></view>
    <view class="step {{currentStep === 'audio' ? 'active' : currentStep === 'answering' || currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">2</text>
      <text class="step-label">听音频</text>
    </view>
    <view class="step-line"></view>
    <view class="step {{currentStep === 'answering' ? 'active' : currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">3</text>
      <text class="step-label">选择答案</text>
    </view>
  </view>

  <!-- 题目内容 -->
  <view class="question-content" wx:if="{{currentQuestionData}}">
    <!-- 阅读阶段 -->
    <view class="reading-section" wx:if="{{currentStep === 'reading'}}">
      <view class="timer-display">
        <text class="timer-text">阅读时间：{{readTimeLeft}}秒</text>
      </view>
      <view class="question-card">
        <view class="question-number">{{currentQuestion}}.</view>
        <view class="question-text">{{currentQuestionData.question}}</view>
        <view class="options-list">
          <view class="option-item" wx:for="{{currentQuestionData.options}}" wx:key="key">
            <text class="option-key">{{item.key}}.</text>
            <text class="option-text">{{item.text}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 音频播放阶段 -->
    <view class="audio-section" wx:if="{{currentStep === 'audio'}}">
      <view class="audio-player">
        <view class="audio-icon {{isPlayingAudio ? 'playing' : ''}}">🎵</view>
        <view class="audio-info">
          <text class="audio-status">{{isPlayingAudio ? '正在播放...' : '音频播放完毕'}}</text>
          <text class="play-count">已播放 {{audioPlayed}}/{{currentQuestionData.listenTimes}} 遍</text>
        </view>
      </view>
      <view class="audio-tip">
        <text>请仔细听音频内容，第{{currentSection}}节音频将播放{{currentQuestionData.listenTimes}}遍</text>
      </view>
    </view>

    <!-- 答题阶段 -->
    <view class="answering-section" wx:if="{{currentStep === 'answering'}}">
      <view class="timer-display">
        <text class="timer-text">作答时间：{{answerTimeLeft}}秒</text>
      </view>
      <view class="question-card">
        <view class="question-number">{{currentQuestion}}.</view>
        <view class="question-text">{{currentQuestionData.question}}</view>
        <view class="options-list">
          <view 
            class="option-item {{selectedAnswer === item.key ? 'selected' : ''}}" 
            wx:for="{{currentQuestionData.options}}" 
            wx:key="key"
            data-answer="{{item.key}}"
            bindtap="selectAnswer"
          >
            <text class="option-key">{{item.key}}.</text>
            <text class="option-text">{{item.text}}</text>
          </view>
        </view>
      </view>
      <view class="submit-section">
        <button 
          class="submit-btn {{selectedAnswer ? 'active' : 'disabled'}}" 
          bindtap="submitAnswer"
          disabled="{{!selectedAnswer}}"
        >
          提交答案
        </button>
      </view>
    </view>

    <!-- 结果显示阶段 -->
    <view class="result-section" wx:if="{{currentStep === 'result' || showResult}}">
      <view class="result-card">
        <view class="result-header">
          <text class="result-icon">{{isCorrect ? '✓' : '✗'}}</text>
          <text class="result-text">{{isCorrect ? '回答正确' : '回答错误'}}</text>
        </view>
        
        <view class="answer-analysis">
          <view class="your-answer">
            <text class="label">你的答案：</text>
            <text class="answer {{isCorrect ? 'correct' : 'wrong'}}">{{selectedAnswer || '未选择'}}</text>
          </view>
          <view class="correct-answer">
            <text class="label">正确答案：</text>
            <text class="answer correct">{{currentQuestionData.correctAnswer}}</text>
          </view>
        </view>

        <view class="action-buttons">
          <button class="action-btn secondary" bindtap="showAudioText">
            查看原文
          </button>
          <button class="action-btn primary" bindtap="nextQuestion">
            {{currentQuestion < totalQuestions ? '下一题' : '完成练习'}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部统计信息 -->
  <view class="bottom-stats">
    <view class="stat-item">
      <text class="stat-label">正确率</text>
      <text class="stat-value">{{correctCount}}/{{currentQuestion > totalQuestions ? totalQuestions : currentQuestion - (showResult ? 0 : 1)}}</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">当前得分</text>
      <text class="stat-value">{{totalScore}}分</text>
    </view>
  </view>
</view> 