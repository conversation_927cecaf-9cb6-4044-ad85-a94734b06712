const app = getApp();

Page({
  data: {
    // 练习数据
    words: [], // 单词列表
    currentIndex: 0, // 当前单词索引
    currentWord: null, // 当前单词数据
    total: 0, // 总单词数
    progress: 0, // 进度百分比

    // 音频播放数据
    audioContext: null, // 音频上下文
    isPlaying: false, // 是否正在播放
    audioProgress: 0, // 音频进度
    currentTime: '00:00', // 当前时间
    duration: '00:00', // 总时长

    // 选项数据
    options: [], // 选项列表
    selectedOption: -1, // 选中的选项索引
    correctIndex: -1, // 正确答案索引
    showResult: false, // 是否显示结果

    // 统计数据
    startTime: 0, // 开始时间
    totalTime: 0, // 总用时
    correctCount: 0, // 正确数量
    mistakes: [], // 错题列表

    // 完成数据
    showCompletion: false, // 是否显示完成提示
    correctRate: 0 // 正确率
  },

  onLoad(options) {
    // 解析任务数据
    const taskData = JSON.parse(options.data);
    
    // 初始化音频上下文
    this.initAudioContext();

    // 加载单词数据
    this.loadWords(taskData.words);
  },

  onUnload() {
    // 销毁音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }
  },

  // 初始化音频上下文
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();
    audioContext.onPlay(() => {
      this.setData({ isPlaying: true });
    });
    audioContext.onPause(() => {
      this.setData({ isPlaying: false });
    });
    audioContext.onStop(() => {
      this.setData({ isPlaying: false });
    });
    audioContext.onEnded(() => {
      this.setData({ 
        isPlaying: false,
        audioProgress: 0,
        currentTime: '00:00'
      });
    });
    audioContext.onTimeUpdate(() => {
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        audioProgress: progress,
        currentTime: this.formatTime(audioContext.currentTime)
      });
    });

    this.setData({ audioContext });
  },

  // 加载单词数据
  loadWords(words) {
    // 随机打乱单词顺序
    this.shuffleArray(words);
    
    this.setData({
      words,
      total: words.length,
      currentWord: words[0],
      startTime: Date.now()
    });

    // 生成选项
    this.generateOptions();
  },

  // 随机打乱数组
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },

  // 生成选项
  generateOptions() {
    const currentWord = this.data.currentWord;
    const allWords = this.data.words;
    let options = [currentWord.word];
    const usedWords = new Set([currentWord.word]);

    // 生成3个错误选项，添加防止无限循环的保护
    let attempts = 0;
    const maxAttempts = Math.min(allWords.length * 10, 1000);

    while (options.length < 4 && attempts < maxAttempts) {
      attempts++;
      const randomWord = allWords[Math.floor(Math.random() * allWords.length)];

      if (randomWord.word && !usedWords.has(randomWord.word)) {
        options.push(randomWord.word);
        usedWords.add(randomWord.word);
      }
    }

    // 如果选项不足，记录警告
    if (options.length < 4) {
      console.warn(`听力练习选项生成不足: 需要4个选项，实际生成${options.length}个选项`);
      console.warn('词库大小:', allWords.length, '尝试次数:', attempts);
    }

    // 随机打乱选项顺序
    this.shuffleArray(options);

    // 记录正确答案的索引
    const correctIndex = options.indexOf(currentWord.word);

    this.setData({
      options,
      correctIndex,
      selectedOption: -1,
      showResult: false
    });

    // 设置音频
    if (this.data.audioContext) {
      this.data.audioContext.src = currentWord.audioUrl;
      this.data.audioContext.onCanplay(() => {
        this.setData({
          duration: this.formatTime(this.data.audioContext.duration)
        });
      });
    }
  },

  // 播放/暂停音频
  togglePlay() {
    if (!this.data.audioContext) return;

    if (this.data.isPlaying) {
      this.data.audioContext.pause();
    } else {
      this.data.audioContext.play();
    }
  },

  // 滑动条变化
  onSliderChange(e) {
    if (!this.data.audioContext) return;

    const value = e.detail.value;
    const time = (value / 100) * this.data.audioContext.duration;
    this.data.audioContext.seek(time);
  },

  // 选择选项
  onOptionSelect(e) {
    if (this.data.showResult) return;

    const index = e.currentTarget.dataset.index;
    const isCorrect = index === this.data.correctIndex;

    this.setData({
      selectedOption: index,
      showResult: true
    });

    if (isCorrect) {
      this.setData({
        correctCount: this.data.correctCount + 1
      });
    } else {
      // 添加到错题列表
      const mistakes = this.data.mistakes;
      mistakes.push(this.data.currentWord);
      this.setData({ mistakes });
    }
  },

  // 下一个单词
  onNextWord() {
    const nextIndex = this.data.currentIndex + 1;
    
    if (nextIndex < this.data.total) {
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex],
        progress: (nextIndex / this.data.total) * 100
      });

      // 生成新的选项
      this.generateOptions();
    } else {
      this.onComplete();
    }
  },

  // 完成练习
  onComplete() {
    // 计算总用时
    const totalTime = Math.floor((Date.now() - this.data.startTime) / 1000);

    // 计算正确率
    const correctRate = Math.floor((this.data.correctCount / this.data.total) * 100);

    this.setData({
      showCompletion: true,
      totalTime,
      correctRate
    });
  },

  // 格式化时间
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 加入错题本
  async onAddToMistakes() {
    const db = wx.cloud.database();
    const mistakes = this.data.mistakes;

    try {
      // 批量添加错题
      for (const mistake of mistakes) {
        await db.collection('mistake_listening').add({
          data: {
            ...mistake,
            createTime: db.serverDate(),
            openid: app.globalData.openid
          }
        });
      }

      wx.showToast({
        title: '已加入错题本',
        icon: 'success'
      });
    } catch (error) {
      console.error('添加错题失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 错题重练
  onRetryMistakes() {
    if (this.data.mistakes.length === 0) return;

    // 构建任务数据
    const taskData = {
      words: this.data.mistakes
    };

    // 跳转到练习页面
    wx.redirectTo({
      url: `/listening-speaking/listening/practice/practice?data=${JSON.stringify(taskData)}`
    });
  },

  // 返回首页
  onBackToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}); 