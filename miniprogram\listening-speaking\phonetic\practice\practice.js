const app = getApp();

Page({
  data: {
    phoneticSounds: [
      // 元音
      {
        type: '单元音',
        sounds: [
          { symbol: 'i:', example: 'see [si:]', description: '长元音，嘴唇扁平' },
          { symbol: 'ɪ', example: 'sit [sɪt]', description: '短元音，嘴唇稍扁' },
          { symbol: 'e', example: 'bed [bed]', description: '中前元音' },
          { symbol: 'æ', example: 'cat [kæt]', description: '低前元音' },
          { symbol: 'ɑ:', example: 'car [kɑ:]', description: '长元音，口大开' },
          { symbol: 'ɔ:', example: 'door [dɔ:]', description: '长元音，嘴唇圆' },
          { symbol: 'ɒ', example: 'hot [hɒt]', description: '短元音，口圆形' },
          { symbol: 'u:', example: 'food [fu:d]', description: '长元音，嘴唇圆' },
          { symbol: 'ʊ', example: 'book [bʊk]', description: '短元音，嘴唇圆' },
          { symbol: 'ʌ', example: 'cup [kʌp]', description: '中央元音' },
          { symbol: 'ə', example: 'about [əˈbaʊt]', description: '中性元音' },
          { symbol: '3:', example: 'bird [b3:d]', description: '长元音，舌尖卷起' }
        ]
      },
      {
        type: '双元音',
        sounds: [
          { symbol: 'eɪ', example: 'day [deɪ]', description: '从/e/向/ɪ/滑动' },
          { symbol: 'aɪ', example: 'my [maɪ]', description: '从/a/向/ɪ/滑动' },
          { symbol: 'ɔɪ', example: 'boy [bɔɪ]', description: '从/ɔ/向/ɪ/滑动' },
          { symbol: 'əʊ', example: 'go [gəʊ]', description: '从/ə/向/ʊ/滑动' },
          { symbol: 'aʊ', example: 'how [haʊ]', description: '从/a/向/ʊ/滑动' },
          { symbol: 'ɪə', example: 'here [hɪə]', description: '从/ɪ/向/ə/滑动' },
          { symbol: 'eə', example: 'hair [heə]', description: '从/e/向/ə/滑动' },
          { symbol: 'ʊə', example: 'sure [ʃʊə]', description: '从/ʊ/向/ə/滑动' }
        ]
      },
      // 辅音
      {
        type: '爆破音',
        sounds: [
          { symbol: 'p', example: 'pen [pen]', description: '清辅音，双唇爆破' },
          { symbol: 'b', example: 'big [bɪg]', description: '浊辅音，双唇爆破' },
          { symbol: 't', example: 'top [tɒp]', description: '清辅音，舌尖爆破' },
          { symbol: 'd', example: 'dog [dɒg]', description: '浊辅音，舌尖爆破' },
          { symbol: 'k', example: 'cat [kæt]', description: '清辅音，舌后爆破' },
          { symbol: 'g', example: 'good [gʊd]', description: '浊辅音，舌后爆破' }
        ]
      },
      {
        type: '摩擦音',
        sounds: [
          { symbol: 'f', example: 'fish [fɪʃ]', description: '清辅音，唇齿摩擦' },
          { symbol: 'v', example: 'very [ˈveri]', description: '浊辅音，唇齿摩擦' },
          { symbol: 'θ', example: 'think [θɪŋk]', description: '清辅音，舌齿摩擦' },
          { symbol: 'ð', example: 'this [ðɪs]', description: '浊辅音，舌齿摩擦' },
          { symbol: 's', example: 'sun [sʌn]', description: '清辅音，舌尖摩擦' },
          { symbol: 'z', example: 'zoo [zu:]', description: '浊辅音，舌尖摩擦' },
          { symbol: 'ʃ', example: 'ship [ʃɪp]', description: '清辅音，舌面摩擦' },
          { symbol: 'ʒ', example: 'vision [ˈvɪʒən]', description: '浊辅音，舌面摩擦' },
          { symbol: 'h', example: 'hat [hæt]', description: '清辅音，气流摩擦' }
        ]
      },
      {
        type: '破擦音',
        sounds: [
          { symbol: 'tʃ', example: 'chair [tʃeə]', description: '清辅音，舌面破擦' },
          { symbol: 'dʒ', example: 'job [dʒɒb]', description: '浊辅音，舌面破擦' },
          { symbol: 'tr', example: 'tree [tri:]', description: '清辅音，舌尖破擦' },
          { symbol: 'dr', example: 'dream [dri:m]', description: '浊辅音，舌尖破擦' },
          { symbol: 'ts', example: 'cats [kæts]', description: '清辅音，舌尖破擦' },
          { symbol: 'dz', example: 'beds [bedz]', description: '浊辅音，舌尖破擦' }
        ]
      },
      {
        type: '鼻音',
        sounds: [
          { symbol: 'm', example: 'man [mæn]', description: '双唇鼻音' },
          { symbol: 'n', example: 'nice [naɪs]', description: '舌尖鼻音' },
          { symbol: 'ŋ', example: 'sing [sɪŋ]', description: '舌后鼻音' }
        ]
      },
      {
        type: '舌边音',
        sounds: [
          { symbol: 'l', example: 'love [lʌv]', description: '舌尖边音' },
          { symbol: 'r', example: 'red [red]', description: '舌尖颤音' }
        ]
      },
      {
        type: '半元音',
        sounds: [
          { symbol: 'w', example: 'water [ˈwɔ:tə]', description: '双唇半元音' },
          { symbol: 'j', example: 'yes [jes]', description: '舌面半元音' }
        ]
      }
    ],
    currentTypeIndex: 0, // 当前选中的音标类型
    practiceMode: false, // 是否进入练习模式
    currentSoundIndex: 0, // 当前练习的音标
    practiceList: [], // 练习列表
    score: 0, // 练习得分
    totalPracticed: 0 // 已练习数量
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '音标专练'
    });
  },

  // 切换音标类型
  onTypeSelect(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTypeIndex: index
    });
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 开始练习某个音标类型
  onStartPractice() {
    const { phoneticSounds, currentTypeIndex } = this.data;
    const currentType = phoneticSounds[currentTypeIndex];
    
    // 随机打乱练习顺序
    const practiceList = [...currentType.sounds].sort(() => Math.random() - 0.5);
    
    this.setData({
      practiceMode: true,
      practiceList,
      currentSoundIndex: 0,
      score: 0,
      totalPracticed: 0
    });

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });
  },

  // 播放音标发音
  onPlaySound(e) {
    const symbol = e.currentTarget.dataset.symbol;
    
    // 这里应该调用TTS播放音标发音
    // 暂时用toast提示
    wx.showToast({
      title: `播放 ${symbol}`,
      icon: 'none',
      duration: 1000
    });

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 练习模式下的评分
  onPracticeAnswer(e) {
    const correct = e.currentTarget.dataset.correct;
    const { score, totalPracticed, practiceList, currentSoundIndex } = this.data;
    
    const newScore = correct ? score + 1 : score;
    const newTotal = totalPracticed + 1;
    
    this.setData({
      score: newScore,
      totalPracticed: newTotal
    });

    // 添加反馈
    if (correct) {
      wx.showToast({
        title: '正确！',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '再试试看',
        icon: 'none',
        duration: 1000
      });
    }

    // 进入下一题
    setTimeout(() => {
      if (currentSoundIndex < practiceList.length - 1) {
        this.setData({
          currentSoundIndex: currentSoundIndex + 1
        });
      } else {
        // 练习完成
        this.showPracticeResult();
      }
    }, 1500);
  },

  // 显示练习结果
  showPracticeResult() {
    const { score, totalPracticed } = this.data;
    const accuracy = Math.round((score / totalPracticed) * 100);
    
    wx.showModal({
      title: '练习完成',
      content: `本次练习得分：${score}/${totalPracticed}\n准确率：${accuracy}%`,
      confirmText: '继续练习',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          this.onStartPractice();
        } else {
          this.setData({
            practiceMode: false
          });
        }
      }
    });
  },

  // 退出练习模式
  onExitPractice() {
    this.setData({
      practiceMode: false
    });
  }
}); 