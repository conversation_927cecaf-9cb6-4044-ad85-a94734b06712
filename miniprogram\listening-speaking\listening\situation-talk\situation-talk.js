Page({
  data: {
    currentStep: 'intro', // intro, scenario, talk, result
    scenarios: [
      {
        id: 1,
        title: '餐厅订餐',
        situation: '你想在一家西餐厅为今晚7点预订一张两人桌，但是被告知没有空位。你需要与服务员协商其他时间或解决方案。',
        setting: '场景：高档西餐厅\n角色：你是顾客，AI是服务员\n目标：成功预订到合适的餐桌',
        keyPhrases: [
          'I\'d like to make a reservation...',
          'Do you have any available tables?',
          'What about earlier/later time?',
          'Is there a waiting list?',
          'Can I book for tomorrow?'
        ],
        aiPrompts: [
          "Good evening! How can I help you today?",
          "I'm sorry, but we're fully booked for 7 PM tonight.",
          "We do have a table available at 6 PM or 8:30 PM. Would either of those work?",
          "Great! May I have your name and phone number for the reservation?",
          "Perfect! Your table for two is confirmed. We look forward to seeing you!"
        ],
        duration: 180, // 3分钟对话时间
        rounds: 5 // 5轮对话
      }
    ],
    currentScenarioIndex: 0,
    currentRound: 0,
    conversationHistory: [],
    userTalking: false,
    aiTalking: false,
    timer: null,
    timeRemaining: 0,
    totalTime: 0,
    userResponses: [], // 存储用户的回应
    showKeyPhrases: false,
    practiceMode: 'normal' // normal, guided
  },

  onLoad: function() {
    console.log('情景对话练习页面加载');
    wx.setNavigationBarTitle({
      title: '情景对话练习'
    });
    this.initializeScenario();
  },

  onUnload: function() {
    this.clearTimer();
    this.stopRecording();
  },

  // 初始化场景
  initializeScenario: function() {
    const scenario = this.data.scenarios[this.data.currentScenarioIndex];
    this.setData({
      timeRemaining: scenario.duration,
      totalTime: scenario.duration,
      conversationHistory: [],
      currentRound: 0,
      userResponses: []
    });
  },

  // 开始计时器
  startTimer: function() {
    this.clearTimer();
    const timer = setInterval(() => {
      const timeRemaining = this.data.timeRemaining - 1;
      this.setData({ timeRemaining });
      
      if (timeRemaining <= 0) {
        this.endConversation();
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 清除计时器
  clearTimer: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
  },

  // 开始对话
  startConversation: function() {
    this.setData({ 
      currentStep: 'talk',
      currentRound: 0
    });
    this.startTimer();
    
    // AI首先发言
    setTimeout(() => {
      this.aiSpeak(0);
    }, 1000);
  },

  // AI发言
  aiSpeak: function(promptIndex) {
    const scenario = this.data.scenarios[this.data.currentScenarioIndex];
    const aiMessage = scenario.aiPrompts[promptIndex] || "Thank you for the conversation!";
    
    this.setData({ aiTalking: true });
    
    // 添加到对话历史
    const conversationHistory = [...this.data.conversationHistory];
    conversationHistory.push({
      speaker: 'ai',
      message: aiMessage,
      timestamp: new Date().getTime()
    });
    
    this.setData({ conversationHistory });
    
    // 模拟AI说话时间
    setTimeout(() => {
      this.setData({ aiTalking: false });
      
      // 如果还没到最后一轮，等待用户回应
      if (this.data.currentRound < scenario.rounds - 1) {
        wx.showToast({
          title: '现在轮到你说话了',
          icon: 'none'
        });
      }
    }, 3000);
  },

  // 用户开始说话
  startUserTalk: function() {
    if (this.data.userTalking || this.data.aiTalking) return;
    
    this.setData({ userTalking: true });
    
    wx.showToast({
      title: '录音开始...',
      icon: 'none'
    });
  },

  // 用户结束说话
  stopUserTalk: function() {
    if (!this.data.userTalking) return;
    
    this.setData({ userTalking: false });
    
    // 模拟用户回应
    const userMessage = "Thank you, that sounds good."; // 实际应该是语音识别结果
    
    // 添加到对话历史
    const conversationHistory = [...this.data.conversationHistory];
    conversationHistory.push({
      speaker: 'user',
      message: userMessage,
      timestamp: new Date().getTime()
    });
    
    // 保存用户回应
    const userResponses = [...this.data.userResponses];
    userResponses.push({
      round: this.data.currentRound,
      message: userMessage,
      timestamp: new Date().getTime()
    });
    
    this.setData({ 
      conversationHistory,
      userResponses,
      currentRound: this.data.currentRound + 1
    });
    
    wx.showToast({
      title: '录音结束',
      icon: 'success'
    });
    
    // 等待一会儿，然后AI回应
    setTimeout(() => {
      const scenario = this.data.scenarios[this.data.currentScenarioIndex];
      if (this.data.currentRound < scenario.rounds) {
        this.aiSpeak(this.data.currentRound);
      } else {
        this.endConversation();
      }
    }, 1500);
  },

  // 停止录音
  stopRecording: function() {
    this.setData({ userTalking: false });
  },

  // 结束对话
  endConversation: function() {
    this.clearTimer();
    this.setData({ currentStep: 'result' });
    
    wx.showModal({
      title: '对话结束',
      content: '恭喜完成情景对话练习！',
      showCancel: false,
      confirmText: '查看结果'
    });
  },

  // 切换关键短语显示
  toggleKeyPhrases: function() {
    this.setData({ showKeyPhrases: !this.data.showKeyPhrases });
  },

  // 切换练习模式
  switchPracticeMode: function() {
    const newMode = this.data.practiceMode === 'normal' ? 'guided' : 'normal';
    this.setData({ practiceMode: newMode });
    
    wx.showToast({
      title: newMode === 'guided' ? '已开启引导模式' : '已关闭引导模式',
      icon: 'none'
    });
  },

  // 查看场景详情
  viewScenarioDetails: function() {
    const scenario = this.data.scenarios[this.data.currentScenarioIndex];
    wx.showModal({
      title: '场景详情',
      content: scenario.setting,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 重新开始
  restart: function() {
    this.setData({
      currentStep: 'intro',
      currentRound: 0,
      conversationHistory: [],
      userTalking: false,
      aiTalking: false,
      userResponses: [],
      showKeyPhrases: false
    });
    this.clearTimer();
    this.initializeScenario();
  },

  // 完成练习
  finishPractice: function() {
    wx.showModal({
      title: '练习完成',
      content: '恭喜完成情景对话练习！',
      confirmText: '返回',
      cancelText: '再练一次',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        } else {
          this.restart();
        }
      }
    });
  },

  // 格式化时间
  formatTime: function(seconds) {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 获取对话进度百分比
  getProgress: function() {
    const scenario = this.data.scenarios[this.data.currentScenarioIndex];
    return Math.round((this.data.currentRound / scenario.rounds) * 100);
  }
}); 