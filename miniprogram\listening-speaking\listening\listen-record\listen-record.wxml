<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title-info">
      <view class="title">听后记录与转述</view>
      <view class="subtitle">共两节，共15分</view>
    </view>
    <view class="score-info">
      <text class="score-text">{{totalScore}}/15分</text>
    </view>
  </view>

  <!-- 流程指示器 -->
  <view class="step-indicator">
    <view class="step {{currentStep === 'preview' ? 'active' : (currentStep === 'listening1' || currentStep === 'listening2' || currentStep === 'listening3') ? 'completed' : ''}}">
      <text class="step-number">1</text>
      <text class="step-label">听后记录</text>
    </view>
    <view class="step-line"></view>
    <view class="step {{(currentStep === 'preparation' || currentStep === 'retelling') ? 'active' : currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">2</text>
      <text class="step-label">转述内容</text>
    </view>
  </view>

  <!-- 预览阶段 -->
  <view class="preview-section" wx:if="{{currentStep === 'preview'}}">
    <!-- 倒计时显示 -->
    <view class="countdown-display">
      <view class="countdown-circle">
        <text class="countdown-number">{{timeLeft}}</text>
        <text class="countdown-unit">秒</text>
      </view>
      <text class="countdown-text">浏览时间</text>
    </view>
    
    <view class="instruction-card">
      <view class="instruction-title">第一节 听后记录信息 (共4小题，每小题1.5分，共6分)</view>
      <view class="instruction-text">
        现在，你有1分钟的时间浏览提示信息。
      </view>
    </view>

    <!-- 标准表格格式 -->
    <view class="standard-table">
      <view class="table-title">About Note Taking</view>
      <view class="table-wrapper">
        <view class="table-row table-header">
          <view class="table-cell step-column"></view>
          <view class="table-cell content-column">Content</view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">First</view>
          <view class="table-cell content-column">
            Focus on the main points, rather than copying down the entire (1)________ or every word.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Second</view>
          <view class="table-cell content-column">
            Write down keywords, dates, names, etc. Quickly.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Third</view>
          <view class="table-cell content-column">
            Take visually clear, (2)________ and structured notes.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Fourth</view>
          <view class="table-cell content-column">
            Use short forms and (3)________.<br/>
            Write in signs and phrases instead of complete sentences.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Lastly</view>
          <view class="table-cell content-column">
            Be consistent with your structure.<br/>
            Pick a (4)________ and stick with it.
          </view>
        </view>
      </view>
    </view>

    <!-- 答案输入区域 -->
    <view class="answer-input-section">
      <view class="input-row">
        <view class="input-item">
          <text class="input-label">1.</text>
          <view class="input-line">____________</view>
        </view>
        <view class="input-item">
          <text class="input-label">2.</text>
          <view class="input-line">____________</view>
        </view>
        <view class="input-item">
          <text class="input-label">3.</text>
          <view class="input-line">____________</view>
        </view>
        <view class="input-item">
          <text class="input-label">4.</text>
          <view class="input-line">____________</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 听力播放阶段（第一遍和第二遍可以填空） -->
  <view class="listening-section" wx:if="{{currentStep === 'listening1' || currentStep === 'listening2'}}">
    <view class="listening-card">
      <view class="listening-header">
        <text class="listening-title">
          {{currentStep === 'listening1' ? '第一遍播放' : '第二遍播放'}}
        </text>
        <text class="play-status">{{isPlaying ? '播放中...' : '播放完成'}}</text>
      </view>
      
      <view class="audio-visual {{isPlaying ? 'playing' : ''}}">
        <view class="wave-container">
          <view class="wave wave1"></view>
          <view class="wave wave2"></view>
          <view class="wave wave3"></view>
          <view class="wave wave4"></view>
        </view>
        <text class="audio-icon">🔊</text>
      </view>
      
      <view class="listening-tips">
        <text>{{currentStep === 'listening1' ? '第一遍播放，请边听边填空' : '第二遍播放，请继续完善填空'}}</text>
      </view>
    </view>

    <!-- 标准表格格式（听力时） -->
    <view class="standard-table">
      <view class="table-title">About Note Taking</view>
      <view class="table-wrapper">
        <view class="table-row table-header">
          <view class="table-cell step-column"></view>
          <view class="table-cell content-column">Content</view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">First</view>
          <view class="table-cell content-column">
            Focus on the main points, rather than copying down the entire (1)________ or every word.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Second</view>
          <view class="table-cell content-column">
            Write down keywords, dates, names, etc. Quickly.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Third</view>
          <view class="table-cell content-column">
            Take visually clear, (2)________ and structured notes.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Fourth</view>
          <view class="table-cell content-column">
            Use short forms and (3)________.<br/>
            Write in signs and phrases instead of complete sentences.
          </view>
        </view>
        <view class="table-row">
          <view class="table-cell step-column">Lastly</view>
          <view class="table-cell content-column">
            Be consistent with your structure.<br/>
            Pick a (4)________ and stick with it.
          </view>
        </view>
      </view>
    </view>

    <!-- 实时答案输入区域 -->
    <view class="answer-input-section active">
      <view class="input-row">
        <view class="input-item">
          <text class="input-label">1.</text>
          <input 
            class="answer-input" 
            placeholder="____________" 
            value="{{answers[0]}}" 
            data-index="0" 
            bindinput="onBlankInput" 
            disabled="{{!canFillBlanks}}"
          />
        </view>
        <view class="input-item">
          <text class="input-label">2.</text>
          <input 
            class="answer-input" 
            placeholder="____________" 
            value="{{answers[1]}}" 
            data-index="1" 
            bindinput="onBlankInput" 
            disabled="{{!canFillBlanks}}"
          />
        </view>
        <view class="input-item">
          <text class="input-label">3.</text>
          <input 
            class="answer-input" 
            placeholder="____________" 
            value="{{answers[2]}}" 
            data-index="2" 
            bindinput="onBlankInput" 
            disabled="{{!canFillBlanks}}"
          />
        </view>
        <view class="input-item">
          <text class="input-label">4.</text>
          <input 
            class="answer-input" 
            placeholder="____________" 
            value="{{answers[3]}}" 
            data-index="3" 
            bindinput="onBlankInput" 
            disabled="{{!canFillBlanks}}"
          />
        </view>
      </view>
    </view>

    <view class="filling-status">
      <text class="status-text">已填写：{{filledCount}}/4 个空格</text>
    </view>
  </view>

  <!-- 第三遍听力播放阶段 -->
  <view class="listening-section" wx:if="{{currentStep === 'listening3'}}">
    <view class="listening-card">
      <view class="listening-header">
        <text class="listening-title">第三遍播放（转述准备）</text>
        <text class="play-status">{{isPlaying ? '播放中...' : '播放完成'}}</text>
      </view>
      
      <view class="audio-visual {{isPlaying ? 'playing' : ''}}">
        <view class="wave-container">
          <view class="wave wave1"></view>
          <view class="wave wave2"></view>
          <view class="wave wave3"></view>
          <view class="wave wave4"></view>
        </view>
        <text class="audio-icon">🔊</text>
      </view>
      
      <view class="listening-tips">
        <text>第三遍播放，请仔细听并准备转述内容</text>
      </view>
    </view>

    <!-- 显示已填写的答案 -->
    <view class="filled-answers">
      <view class="answers-title">您的填空答案：</view>
      <view class="answer-list">
        <view class="answer-item" wx:for="{{4}}" wx:key="*this">
          <text class="answer-number">{{index + 1}}.</text>
          <text class="answer-text">{{answers[index] || '未填写'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 转述准备阶段 -->
  <view class="preparation-section" wx:if="{{currentStep === 'preparation'}}">
    <view class="timer-display">
      <text class="timer-text">准备时间：{{formatTime(timeLeft)}}</text>
    </view>
    
    <view class="instruction-card">
      <view class="instruction-title">第二节 转述短文内容 (本节共9分)</view>
      <view class="instruction-text">
        现在，你有2分钟的时间做转述准备，转述的开头已给出。
      </view>
    </view>

    <view class="retelling-start">
      <view class="start-title">转述开头：</view>
      <view class="start-text">{{retellingStartText}}</view>
    </view>

    <view class="preparation-tips">
      <view class="tips-title">转述要点：</view>
      <view class="tip-item">• 包含短文的主要内容和关键信息</view>
      <view class="tip-item">• 语言表达清晰流畅</view>
      <view class="tip-item">• 时间控制在2分钟内</view>
      <view class="tip-item">• 可以适当重组语言，但不改变原意</view>
    </view>
  </view>

  <!-- 转述录音阶段 -->
  <view class="retelling-section" wx:if="{{currentStep === 'retelling'}}">
    <view class="recording-display">
      <view class="timer-display">
        <text class="timer-text">转述时间：{{formatTime(timeLeft)}}</text>
      </view>
      <view class="record-time">
        <text class="record-text">已录制：{{formatTime(recordTime)}}</text>
      </view>
    </view>

    <view class="recording-card">
      <view class="recording-icon {{isRecording ? 'recording' : ''}}">🎙️</view>
      <view class="recording-status">
        <text class="status-text">{{isRecording ? '正在录音转述中...' : '录音已停止'}}</text>
      </view>
    </view>

    <view class="retelling-prompt">
      <view class="prompt-title">转述开头：</view>
      <view class="prompt-text">{{retellingStartText}}</view>
    </view>

    <view class="recording-controls" wx:if="{{isRecording}}">
      <button class="control-btn stop-btn" bindtap="stopRetelling">
        完成转述
      </button>
    </view>
  </view>

  <!-- 结果显示阶段 -->
  <view class="result-section" wx:if="{{currentStep === 'result'}}">
    <view class="result-card">
      <view class="result-header">
        <text class="result-icon">🎉</text>
        <text class="result-title">练习完成</text>
      </view>

      <view class="score-breakdown">
        <view class="score-item">
          <text class="score-label">第一节 听后记录：</text>
          <text class="score-value">{{fillingScore}}/6分</text>
        </view>
        <view class="score-item">
          <text class="score-label">第二节 转述内容：</text>
          <text class="score-value">{{retellingScore}}/9分</text>
        </view>
        <view class="score-total">
          <text class="total-label">总分：</text>
          <text class="total-value">{{totalScore}}/15分</text>
        </view>
      </view>

      <view class="result-actions">
        <button class="action-btn secondary" bindtap="showAnswers">
          查看答案
        </button>
        <button class="action-btn secondary" bindtap="showAudioText">
          听力原文
        </button>
        <button class="action-btn primary" bindtap="goBack">
          返回练习
        </button>
      </view>
    </view>
  </view>
</view> 