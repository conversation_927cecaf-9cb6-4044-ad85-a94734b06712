<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title-info">
      <view class="title">朗读短文并回答问题</view>
      <view class="subtitle">共两节，共14分</view>
    </view>
    <view class="score-info">
      <text class="score-text">{{totalScore}}/14分</text>
    </view>
  </view>

  <!-- 流程指示器 -->
  <view class="step-indicator">
    <view class="step {{currentStep === 'reading' || currentStep === 'reading_prepare' || currentStep === 'reading_record' ? 'active' : currentStep === 'questions_prepare' || currentStep === 'answering' || currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">1</text>
      <text class="step-label">短文朗读</text>
    </view>
    <view class="step-line"></view>
    <view class="step {{currentStep === 'questions_prepare' || currentStep === 'answering' ? 'active' : currentStep === 'result' ? 'completed' : ''}}">
      <text class="step-number">2</text>
      <text class="step-label">回答问题</text>
    </view>
  </view>

  <!-- 阅读文章阶段 -->
  <view class="reading-section" wx:if="{{currentStep === 'reading'}}">
    <!-- 倒计时显示 -->
    <view class="countdown-display">
      <view class="countdown-circle">
        <text class="countdown-number">{{timeLeft}}</text>
        <text class="countdown-unit">秒</text>
      </view>
      <text class="countdown-text">浏览时间</text>
    </view>
    
    <view class="instruction-card">
      <view class="instruction-title">第一节 短文朗读 (本节共8分)</view>
      <view class="instruction-text">现在，你有1分钟的时间浏览短文与问题，并做录音准备。</view>
    </view>

    <view class="article-container">
      <view class="article-content">
        <text class="article-text">{{article.content}}</text>
      </view>
    </view>

    <view class="questions-preview">
      <view class="preview-title">问题预览：</view>
      <view class="question-item" wx:for="{{questions}}" wx:key="id">
        <text class="question-number">{{index + 1}}.</text>
        <text class="question-text">{{item.question}}</text>
      </view>
    </view>
  </view>

  <!-- 朗读录音阶段 -->
  <view class="reading-record-section" wx:if="{{currentStep === 'reading_record'}}">
    <view class="recording-display">
      <!-- 倒计时显示 -->
      <view class="countdown-display">
        <view class="countdown-circle recording">
          <text class="countdown-number">{{timeLeft}}</text>
          <text class="countdown-unit">秒</text>
        </view>
        <text class="countdown-text">朗读时间</text>
      </view>
      <view class="record-time">
        <text class="record-text">已录制：{{formatTime(recordTime)}}</text>
      </view>
    </view>

    <view class="recording-card">
      <view class="recording-icon {{isRecording ? 'recording' : ''}}">📖</view>
      <view class="recording-status">
        <text class="status-text">{{isRecording ? '正在朗读录音中...' : '朗读录音已停止'}}</text>
      </view>
    </view>

    <view class="article-display">
      <view class="article-content">
        <text class="article-text">{{article.content}}</text>
      </view>
    </view>

    <view class="recording-controls" wx:if="{{isRecording}}">
      <button class="control-btn stop-btn" bindtap="stopReadingRecord">
        完成朗读
      </button>
    </view>
  </view>

  <!-- 问题准备阶段 -->
  <view class="question-prepare-section" wx:if="{{currentStep === 'questions_prepare'}}">
    <!-- 倒计时显示 -->
    <view class="countdown-display">
      <view class="countdown-circle prepare">
        <text class="countdown-number">{{timeLeft}}</text>
        <text class="countdown-unit">秒</text>
      </view>
      <text class="countdown-text">准备时间</text>
    </view>
    
    <view class="question-card">
      <view class="question-header">
        <text class="question-title">第{{currentQuestionIndex + 1}}题</text>
        <text class="question-info">准备15秒 | 作答30秒</text>
      </view>
      
      <view class="current-question">
        <text class="question-text">{{questions[currentQuestionIndex].question}}</text>
      </view>
      
      <view class="prepare-tips">
        <text class="tip-text">请仔细思考问题，准备你的回答</text>
      </view>
    </view>
  </view>

  <!-- 回答问题阶段 -->
  <view class="answering-section" wx:if="{{currentStep === 'answering'}}">
    <view class="recording-display">
      <!-- 倒计时显示 -->
      <view class="countdown-display">
        <view class="countdown-circle answering">
          <text class="countdown-number">{{timeLeft}}</text>
          <text class="countdown-unit">秒</text>
        </view>
        <text class="countdown-text">作答时间</text>
      </view>
      <view class="record-time">
        <text class="record-text">已录制：{{formatTime(recordTime)}}</text>
      </view>
    </view>

    <view class="recording-card">
      <view class="recording-icon {{isRecording ? 'recording' : ''}}">🎙️</view>
      <view class="recording-status">
        <text class="status-text">{{isRecording ? '正在录音回答中...' : '录音已停止'}}</text>
      </view>
    </view>

    <view class="question-display">
      <view class="question-header">
        <text class="question-title">第{{currentQuestionIndex + 1}}题</text>
        <text class="progress-info">{{currentQuestionIndex + 1}}/{{questions.length}}</text>
      </view>
      
      <view class="current-question">
        <text class="question-text">{{questions[currentQuestionIndex].question}}</text>
      </view>
    </view>

    <view class="recording-controls" wx:if="{{isRecording}}">
      <button class="control-btn stop-btn" bindtap="finishCurrentQuestion">
        完成回答
      </button>
    </view>
  </view>

  <!-- 结果显示阶段 -->
  <view class="result-section" wx:if="{{currentStep === 'result'}}">
    <view class="result-card">
      <view class="result-header">
        <text class="result-icon">🎉</text>
        <text class="result-title">练习完成</text>
      </view>

      <view class="score-breakdown">
        <view class="score-item">
          <text class="score-label">第一节 短文朗读：</text>
          <text class="score-value">{{readingScore}}/8分</text>
        </view>
        <view class="score-item">
          <text class="score-label">第二节 回答问题：</text>
          <text class="score-value">{{questionsScore}}/6分</text>
        </view>
        <view class="score-total">
          <text class="total-label">总分：</text>
          <text class="total-value">{{totalScore}}/14分</text>
        </view>
      </view>

      <view class="questions-summary">
        <view class="summary-title">答题详情：</view>
        <view class="question-summary" wx:for="{{questions}}" wx:key="id">
          <view class="summary-question">
            <text class="summary-number">{{index + 1}}.</text>
            <text class="summary-text">{{item.question}}</text>
          </view>
          <view class="summary-score">{{item.score || 0}}/2分</view>
        </view>
      </view>

      <view class="result-actions">
        <button class="action-btn secondary" bindtap="showReferenceAnswers">
          查看答案
        </button>
        <button class="action-btn primary" bindtap="goBack">
          返回练习
        </button>
      </view>
    </view>
  </view>
</view> 