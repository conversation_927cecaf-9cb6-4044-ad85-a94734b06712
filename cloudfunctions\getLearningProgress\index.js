const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 艾宾浩斯记忆曲线间隔（天）
const EBBINGHAUS_INTERVALS = [1, 2, 4, 7, 15, 30, 60]

// 计算下次复习时间
function calculateNextReviewTime(masteryLevel, lastReviewTime) {
  const now = new Date()
  const lastReview = new Date(lastReviewTime)
  
  // 根据掌握程度确定复习间隔
  let intervalDays = EBBINGHAUS_INTERVALS[Math.min(masteryLevel - 1, EBBINGHAUS_INTERVALS.length - 1)]
  
  // 计算下次复习时间
  const nextReviewTime = new Date(lastReview.getTime() + intervalDays * 24 * 60 * 60 * 1000)
  
  return {
    nextReviewTime: nextReviewTime,
    intervalDays: intervalDays,
    isReviewDue: now >= nextReviewTime
  }
}

// 获取需要复习的词汇
function getReviewWords(progressData) {
  if (!progressData.wordProgress || !Array.isArray(progressData.wordProgress)) {
    return []
  }
  
  const now = new Date()
  const reviewWords = []
  
  progressData.wordProgress.forEach(wordData => {
    if (wordData.masteryLevel && wordData.lastReviewTime) {
      const reviewInfo = calculateNextReviewTime(wordData.masteryLevel, wordData.lastReviewTime)
      if (reviewInfo.isReviewDue) {
        reviewWords.push({
          ...wordData,
          ...reviewInfo
        })
      }
    }
  })
  
  // 按优先级排序：掌握程度低的优先，过期时间长的优先
  reviewWords.sort((a, b) => {
    if (a.masteryLevel !== b.masteryLevel) {
      return a.masteryLevel - b.masteryLevel // 掌握程度低的优先
    }
    return new Date(a.nextReviewTime) - new Date(b.nextReviewTime) // 过期时间长的优先
  })
  
  return reviewWords
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { libraryId, mode, includeReviewWords = false } = event

    console.log('获取学习进度:', {
      openid: wxContext.OPENID,
      libraryId,
      mode,
      includeReviewWords
    })

    let query = db.collection('learning_progress')
      .where({
        openid: wxContext.OPENID
      })

    // 如果指定了词库和模式，添加过滤条件
    if (libraryId) {
      query = query.where({
        openid: wxContext.OPENID,
        libraryId: libraryId
      })
      
      if (mode) {
        query = query.where({
          openid: wxContext.OPENID,
          libraryId: libraryId,
          mode: mode
        })
      }
    }

    const result = await query
      .orderBy('updateTime', 'desc')
      .get()

    console.log('查询到的学习进度:', result.data.length, '条')

    // 处理学习进度数据
    const progressList = result.data.map(progress => {
      const processedProgress = {
        ...progress,
        // 计算复习相关信息
        reviewInfo: includeReviewWords ? {
          reviewWords: getReviewWords(progress),
          totalReviewWords: getReviewWords(progress).length
        } : null
      }
      
      return processedProgress
    })

    return {
      success: true,
      message: '获取学习进度成功',
      data: progressList
    }

  } catch (error) {
    console.error('获取学习进度失败:', error)
    return {
      success: false,
      message: '获取学习进度失败',
      error: error.message
    }
  }
}
