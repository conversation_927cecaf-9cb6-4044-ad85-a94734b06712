const app = getApp();

Page({
  data: {
    // 国际音标数据
    phonetics: {
      // 元音音标
      vowels: [
        { symbol: 'iː', name: '长音i', example: 'see' },
        { symbol: 'ɪ', name: '短音i', example: 'sit' },
        { symbol: 'e', name: '短音e', example: 'bed' },
        { symbol: 'æ', name: '短音æ', example: 'cat' },
        { symbol: 'ɑː', name: '长音a', example: 'car' },
        { symbol: 'ɒ', name: '短音o', example: 'pot' },
        { symbol: 'ɔː', name: '长音o', example: 'saw' },
        { symbol: 'ʊ', name: '短音u', example: 'put' },
        { symbol: 'uː', name: '长音u', example: 'too' },
        { symbol: 'ʌ', name: '短音ʌ', example: 'cup' },
        { symbol: 'ɜː', name: '长音ɜ', example: 'bird' },
        { symbol: 'ə', name: '弱音ə', example: 'about' },
        { symbol: 'eɪ', name: '双元音eɪ', example: 'day' },
        { symbol: 'aɪ', name: '双元音aɪ', example: 'my' },
        { symbol: 'ɔɪ', name: '双元音ɔɪ', example: 'boy' },
        { symbol: 'aʊ', name: '双元音aʊ', example: 'how' },
        { symbol: 'əʊ', name: '双元音əʊ', example: 'go' },
        { symbol: 'ɪə', name: '双元音ɪə', example: 'here' },
        { symbol: 'eə', name: '双元音eə', example: 'hair' },
        { symbol: 'ʊə', name: '双元音ʊə', example: 'poor' }
      ],
      // 辅音音标
      consonants: [
        { symbol: 'p', name: '清音p', example: 'pen' },
        { symbol: 'b', name: '浊音b', example: 'big' },
        { symbol: 't', name: '清音t', example: 'ten' },
        { symbol: 'd', name: '浊音d', example: 'dog' },
        { symbol: 'k', name: '清音k', example: 'cat' },
        { symbol: 'g', name: '浊音g', example: 'go' },
        { symbol: 'f', name: '清音f', example: 'five' },
        { symbol: 'v', name: '浊音v', example: 'very' },
        { symbol: 'θ', name: '清音θ', example: 'think' },
        { symbol: 'ð', name: '浊音ð', example: 'this' },
        { symbol: 's', name: '清音s', example: 'sun' },
        { symbol: 'z', name: '浊音z', example: 'zoo' },
        { symbol: 'ʃ', name: '清音ʃ', example: 'she' },
        { symbol: 'ʒ', name: '浊音ʒ', example: 'pleasure' },
        { symbol: 'tʃ', name: '清音tʃ', example: 'cheese' },
        { symbol: 'dʒ', name: '浊音dʒ', example: 'job' },
        { symbol: 'm', name: '鼻音m', example: 'man' },
        { symbol: 'n', name: '鼻音n', example: 'no' },
        { symbol: 'ŋ', name: '鼻音ŋ', example: 'ring' },
        { symbol: 'l', name: '舌音l', example: 'love' },
        { symbol: 'r', name: '舌音r', example: 'red' },
        { symbol: 'j', name: '半元音j', example: 'yes' },
        { symbol: 'w', name: '半元音w', example: 'we' },
        { symbol: 'h', name: '气音h', example: 'hat' }
      ]
    },
    
    // 显示用的音标数据（包含选中状态）
    displayPhonetics: {
      vowels: [],
      consonants: []
    },
    
    // 选择状态
    selectedPhonetics: [], // 选中的音标
    selectionMode: 'single', // 选择模式：single 单选，multiple 多选
    activeTab: 'vowels', // 当前选中的标签页
    
    // 生成结果
    showResult: false, // 是否显示结果
    result: '', // 生成的音标学习内容
    isProcessing: false, // 是否正在生成
    
    // 历史记录
    history: [] // 历史记录
  },

  onLoad: function() {
    console.log('=== 音标专练页面加载 ===');
    // 初始化页面数据
    this.initPageData();
    // 初始化显示数据
    this.updateDisplayPhonetics();
  },

  onShow: function() {
    console.log('=== 音标专练页面显示 ===');
  },

  // 初始化页面数据
  initPageData: function() {
    // 从本地存储加载历史记录
    try {
      const history = wx.getStorageSync('phonetics_history') || [];
      this.setData({ history });
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  },

  // 更新显示用的音标数据
  updateDisplayPhonetics: function() {
    const selectedPhonetics = this.data.selectedPhonetics;
    const phonetics = this.data.phonetics;
    
    // 为每个音标添加选中状态
    const displayPhonetics = {
      vowels: phonetics.vowels.map(item => ({
        ...item,
        selected: selectedPhonetics.some(p => p.symbol === item.symbol)
      })),
      consonants: phonetics.consonants.map(item => ({
        ...item,
        selected: selectedPhonetics.some(p => p.symbol === item.symbol)
      }))
    };
    
    this.setData({ displayPhonetics });
  },

  // 切换标签页
  onTabTap: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  // 切换选择模式
  onSelectionModeChange: function(e) {
    const mode = e.detail.value;
    this.setData({ 
      selectionMode: mode,
      selectedPhonetics: [] // 切换模式时清空选择
    });
    // 更新显示数据
    this.updateDisplayPhonetics();
  },

  // 音标选择
  onPhoneticTap: function(e) {
    const phonetic = e.currentTarget.dataset.phonetic;
    let selectedPhonetics = [...this.data.selectedPhonetics];
    
    if (this.data.selectionMode === 'single') {
      // 单选模式
      selectedPhonetics = [phonetic];
    } else {
      // 多选模式
      const index = selectedPhonetics.findIndex(p => p.symbol === phonetic.symbol);
      if (index > -1) {
        selectedPhonetics.splice(index, 1); // 取消选择
      } else {
        selectedPhonetics.push(phonetic); // 添加选择
      }
    }
    
    this.setData({ selectedPhonetics });
    // 更新显示数据
    this.updateDisplayPhonetics();
  },

  // 清空选择
  onClearSelection: function() {
    this.setData({ selectedPhonetics: [] });
    // 更新显示数据
    this.updateDisplayPhonetics();
  },

  // 全选当前标签页
  onSelectAll: function() {
    const currentPhonetics = this.data.phonetics[this.data.activeTab];
    this.setData({ selectedPhonetics: [...currentPhonetics] });
    // 更新显示数据
    this.updateDisplayPhonetics();
  },

  // 生成音标学习内容
  onGenerateContent: async function() {
    if (this.data.selectedPhonetics.length === 0) {
      wx.showToast({
        title: '请先选择音标',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({ isProcessing: true });
    wx.showLoading({
      title: '生成中...',
      mask: true
    });

    try {
      // 获取用户OpenID
      const app = getApp();
      let openid = app.globalData.openid || wx.getStorageSync('openid');
      
      if (!openid) {
        try {
          await this.getOpenId();
          openid = app.globalData.openid || wx.getStorageSync('openid');
        } catch (error) {
          console.error('获取用户身份失败:', error);
        }
      }
      
      if (!openid) {
        throw new Error('用户身份验证失败，请重新登录');
      }

      // 构建音标学习提示
      const phoneticList = this.data.selectedPhonetics.map(p => 
        `${p.symbol} (${p.name}) - 例词: ${p.example}`
      ).join('\n');

      const promptText = `请为以下国际音标提供详细的学习指导：\n\n${phoneticList}\n\n请为每个音标提供：\n1. 发音要点和口型描述\n2. 发音技巧和练习方法\n3. 常见发音错误和纠正方法\n4. 相似音标的对比区别\n5. 练习单词和口诀记忆法`;

      // 调用音标专练云函数
      const result = await wx.cloud.callFunction({
        name: 'phoneticPractice',
        data: {
          assistant_id: 'D0Frv4Z0KWlh',
          user_id: openid,
          stream: false,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: promptText
                }
              ]
            }
          ]
        },
        config: {
          timeout: 30000 // 设置30秒超时
        }
      });

      wx.hideLoading();
      
      console.log('云函数调用返回结果:', JSON.stringify(result, null, 2));
      
      if (result.result && (result.result.success === true || result.result.code === 200)) {
        let content;
        if (result.result.data?.content) {
          content = result.result.data.content;
        } else if (result.result.data?.choices?.[0]?.message?.content) {
          content = result.result.data.choices[0].message.content;
        }
        
        if (!content) {
          throw new Error('生成内容为空，请重试');
        }
        
        this.setData({ 
          result: content,
          showResult: true,
          isProcessing: false 
        });
        
        // 保存到历史记录
        this.saveToHistory(content);
        
        wx.showToast({
          title: '生成成功',
          icon: 'success',
          duration: 1500
        });
        
        // 滚动到结果区域
        wx.pageScrollTo({
          selector: '.result-section',
          duration: 500
        });
      } else {
        throw new Error(result.result?.message || '生成失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('音标学习内容生成失败:', error);
      this.setData({ isProcessing: false });
      
      // 生成备用内容
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        console.log('检测到超时错误，显示备用内容');
        const backupContent = this.generateBackupContent();
        this.setData({ 
          result: backupContent,
          showResult: true,
          isProcessing: false 
        });
        
        wx.showToast({
          title: '已生成备用内容',
          icon: 'success',
          duration: 2000
        });
        
        wx.pageScrollTo({
          selector: '.result-section',
          duration: 500
        });
        
        return;
      }
      
      let errorMessage = '生成失败，请稍后重试';
      if (error.message.includes('用户身份')) {
        errorMessage = '用户身份验证失败，请重新登录';
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络后重试';
      }
      
      wx.showModal({
        title: '生成失败',
        content: errorMessage,
        showCancel: true,
        cancelText: '重试',
        confirmText: '确定',
        success: (res) => {
          if (res.cancel) {
            setTimeout(() => {
              this.onGenerateContent();
            }, 500);
          }
        }
      });
    }
  },

  // 生成备用内容
  generateBackupContent: function() {
    const phoneticList = this.data.selectedPhonetics;
    let content = '🎯 音标学习指导\n\n';
    
    phoneticList.forEach((phonetic, index) => {
      content += `📢 ${phonetic.symbol} (${phonetic.name})\n`;
      content += `💡 发音要点：注意口型和舌位\n`;
      content += `🔤 例词：${phonetic.example}\n`;
      content += `📝 练习：反复朗读并模仿标准发音\n`;
      if (index < phoneticList.length - 1) content += '\n';
    });
    
    content += '\n⚠️ 这是备用模式生成的基础指导\n如需更详细的AI学习指导，请稍后重试';
    
    return content;
  },

  // 保存到历史记录
  saveToHistory: function(content) {
    try {
      const history = this.data.history;
      const newRecord = {
        id: Date.now(),
        phonetics: [...this.data.selectedPhonetics],
        content: content,
        timestamp: new Date().toISOString()
      };
      
      history.unshift(newRecord);
      if (history.length > 10) { // 保留最近10条记录
        history.splice(10);
      }
      
      this.setData({ history });
      wx.setStorageSync('phonetics_history', history);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  // 重新生成
  onRegenerate: function() {
    this.onGenerateContent();
  },

  // 复制内容
  onCopyContent: function() {
    if (!this.data.result) {
      wx.showToast({
        title: '没有内容可复制',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.result,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 清空结果
  onClearResult: function() {
    this.setData({
      result: '',
      showResult: false,
      selectedPhonetics: []
    });
    // 更新显示数据
    this.updateDisplayPhonetics();
  },

  // 获取用户OpenID
  getOpenId: function() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            wx.cloud.callFunction({
              name: 'login',
              data: { code: res.code },
              success: (result) => {
                if (result.result && result.result.success) {
                  const openid = result.result.data.openid;
                  getApp().globalData.openid = openid;
                  wx.setStorageSync('openid', openid);
                  resolve(openid);
                } else {
                  reject(new Error('登录验证失败'));
                }
              },
              fail: reject
            });
          } else {
            reject(new Error('获取登录凭证失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '音标专练 - 掌握标准发音技巧',
      path: '/listening-speaking/phonetics/phonetics',
      imageUrl: '/assets/icons/logo.png'
    };
  }
}); 