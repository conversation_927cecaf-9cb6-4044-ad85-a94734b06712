Page({
  data: {
    currentStep: 'instruction', // instruction, reading, reading_record, questions_prepare, answering, result
    
    // 文章内容
    article: {
      content: `<PERSON> is 13 years old. She lives on the farm with her parents, her brothers <PERSON> and <PERSON>. The <PERSON>'s farm is in the Outback - it's very hot and dry, and not many people live there. So how do <PERSON> and her brothers go to school? Well, they don't go to school - their school comes to them, by radio and the Internet. Their classmates live on other farms and their teachers work at the "School of the Air" in a place called Alice Springs. When the pupils have to do homework, they send it to their teachers by fax or the Internet. The "School of the Air" is only for younger students. So next year, when <PERSON> is 14, she has to go to a school in Alice Springs. She is going to live at the school and only visit her family during vacations.`
    },
    
    // 问题列表
    questions: [
      {
        id: 1,
        question: 'What is the weather like in the Outback?',
        referenceAnswer: 'It\'s very hot and dry.',
        userAnswer: '',
        prepareTime: 15, // 准备时间15秒
        answerTime: 30   // 作答时间30秒
      },
      {
        id: 2,
        question: 'How do the pupils send their homework to their teachers?',
        referenceAnswer: 'By fax or the Internet.',
        userAnswer: '',
        prepareTime: 15,
        answerTime: 30
      },
      {
        id: 3,
        question: 'Why will <PERSON> only visit her family during the vacations next year?',
        referenceAnswer: 'She has to go to a school in Alice Springs and live at the school.',
        userAnswer: '',
        prepareTime: 15,
        answerTime: 30
      }
    ],
    
    currentQuestionIndex: 0,
    
    // 时间控制
    readingTime: 60, // 浏览时间1分钟
    readingRecordTime: 120, // 朗读录音时间2分钟
    timeLeft: 0,
    
    // 录音控制
    isRecording: false,
    recordTime: 0,
    
    // 成绩统计
    readingScore: 0,
    questionsScore: 0,
    totalScore: 0,
    
    timer: null
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '朗读短文并回答问题'
    });
    this.showInstruction();
  },

  onUnload() {
    this.clearTimer();
  },

  // 显示说明
  showInstruction() {
    wx.showModal({
      title: '朗读短文并回答问题',
      content: '本大题共两节。第一节，朗读一段短文。第二节，根据所朗读的内容口头回答问题。现在，你有1分钟的时间浏览短文与问题，并做录音准备。',
      showCancel: false,
      confirmText: '开始浏览',
      success: () => {
        this.startReading();
      }
    });
  },

  // 开始浏览文章
  startReading() {
    this.setData({
      currentStep: 'reading',
      timeLeft: this.data.readingTime
    });
    
    wx.showToast({
      title: '现在有1分钟时间浏览文章和问题',
      icon: 'none',
      duration: 2000
    });
    
    this.startTimer();
  },

  // 开始朗读录音
  startReadingRecord() {
    wx.showModal({
      title: '第一节 短文朗读',
      content: '浏览时间结束。现在开始朗读短文，录音时间为2分钟。',
      showCancel: false,
      confirmText: '开始朗读',
      success: () => {
        this.setData({
          currentStep: 'reading_record',
          timeLeft: this.data.readingRecordTime,
          isRecording: true,
          recordTime: 0
        });
        
        wx.showToast({
          title: '开始朗读录音，时间2分钟',
          icon: 'none',
          duration: 2000
        });
        
        this.startTimer();
        this.startRecordTimer();
      }
    });
  },

  // 停止朗读录音
  stopReadingRecord() {
    this.setData({
      isRecording: false
    });
    
    this.clearTimer();
    
    // 模拟朗读评分
    const readingScore = Math.floor(Math.random() * 3) + 6; // 6-8分随机
    this.setData({
      readingScore
    });
    
    wx.showModal({
      title: '朗读完成',
      content: `朗读得分：${readingScore}/8分\n朗读时长：${this.formatTime(this.data.recordTime)}\n\n现在开始第二节回答问题`,
      showCancel: false,
      confirmText: '开始答题',
      success: () => {
        this.startQuestions();
      }
    });
  },

  // 开始回答问题
  startQuestions() {
    this.setData({
      currentStep: 'questions_prepare',
      currentQuestionIndex: 0
    });
    
    this.prepareCurrentQuestion();
  },

  // 准备当前问题
  prepareCurrentQuestion() {
    const currentQuestion = this.data.questions[this.data.currentQuestionIndex];
    const questionNum = this.data.currentQuestionIndex + 1;
    
    wx.showModal({
      title: `第${questionNum}题准备`,
      content: `回答每道问题前，你将有15秒钟的时间进行准备。每道问题你将有30秒钟的作答时间。\n\n问题：${currentQuestion.question}`,
      showCancel: false,
      confirmText: '开始准备',
      success: () => {
        this.setData({
          currentStep: 'questions_prepare',
          timeLeft: currentQuestion.prepareTime
        });
        this.startTimer();
      }
    });
  },

  // 开始回答当前问题
  startAnswerCurrentQuestion() {
    const currentQuestion = this.data.questions[this.data.currentQuestionIndex];
    
    this.setData({
      currentStep: 'answering',
      timeLeft: currentQuestion.answerTime,
      isRecording: true,
      recordTime: 0
    });
    
    wx.showToast({
      title: '开始录音回答',
      icon: 'none'
    });
    
    this.startTimer();
    this.startRecordTimer();
  },

  // 完成当前问题回答
  finishCurrentQuestion() {
    this.setData({
      isRecording: false
    });
    
    this.clearTimer();
    
    const { currentQuestionIndex, questions } = this.data;
    
    // 模拟评分
    const questionScore = Math.floor(Math.random() * 3) + 1; // 1-3分随机
    const updatedQuestions = [...questions];
    updatedQuestions[currentQuestionIndex].score = questionScore;
    
    this.setData({
      questions: updatedQuestions
    });
    
    if (currentQuestionIndex < questions.length - 1) {
      // 还有下一题
      wx.showModal({
        title: `第${currentQuestionIndex + 1}题完成`,
        content: `回答时长：${this.formatTime(this.data.recordTime)}\n得分：${questionScore}/2分\n\n准备下一题`,
        showCancel: false,
        confirmText: '下一题',
        success: () => {
          this.setData({
            currentQuestionIndex: currentQuestionIndex + 1
          });
          this.prepareCurrentQuestion();
        }
      });
    } else {
      // 所有题目完成
      this.finishAllQuestions();
    }
  },

  // 完成所有问题
  finishAllQuestions() {
    const questionsScore = this.data.questions.reduce((total, q) => total + (q.score || 0), 0);
    const totalScore = this.data.readingScore + questionsScore;
    
    // 收集低分题目作为错题
    this.collectLowScoreQuestions();
    
    this.setData({
      questionsScore,
      totalScore,
      currentStep: 'result'
    });
  },

  // 计时器
  startTimer() {
    this.clearTimer();
    
    const timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });
      
      if (timeLeft <= 0) {
        this.clearTimer();
        this.handleTimeUp();
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 录音计时器
  startRecordTimer() {
    const recordTimer = setInterval(() => {
      if (!this.data.isRecording) {
        clearInterval(recordTimer);
        return;
      }
      
      this.setData({
        recordTime: this.data.recordTime + 1
      });
    }, 1000);
  },

  // 清除计时器
  clearTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }
  },

  // 时间到处理
  handleTimeUp() {
    const { currentStep } = this.data;
    
    if (currentStep === 'reading') {
      this.startReadingRecord();
    } else if (currentStep === 'reading_record') {
      this.stopReadingRecord();
    } else if (currentStep === 'questions_prepare') {
      this.startAnswerCurrentQuestion();
    } else if (currentStep === 'answering') {
      this.finishCurrentQuestion();
    }
  },

  // 查看参考答案
  showReferenceAnswers() {
    const answersText = this.data.questions.map((q, index) => 
      `${index + 1}. ${q.question}\n参考答案：${q.referenceAnswer}`
    ).join('\n\n');
    
    wx.showModal({
      title: '参考答案',
      content: answersText,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 格式化时间
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 返回
  goBack() {
    wx.navigateBack();
  },

  // 收集低分题目作为错题
  collectLowScoreQuestions() {
    const app = getApp();
    
    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      console.log('用户未登录或openid不可用，跳过错题收集');
      return;
    }
    
    const userId = app.getUserOpenId();

    const lowScoreQuestions = this.data.questions.filter(q => (q.score || 0) < 2); // 低于2分的题目
    
    if (lowScoreQuestions.length === 0) {
      return;
    }

    lowScoreQuestions.forEach((question, index) => {
      const mistakeRecord = {
        article: this.data.article.content,
        question: question.question,
        referenceAnswer: question.referenceAnswer,
        userScore: question.score || 0,
        maxScore: 2,
        mistakeType: 'reading_answer', // 标识来源为朗读短文并回答问题
        section: 'oral_answer', // 口头回答部分
        questionId: question.id,
        createTime: new Date()
      };

      wx.cloud.callFunction({
        name: 'addMistake',
        data: {
          userId: userId,
          wordId: `read_answer_${question.id}`,
          type: 'listening', // 归类到听口错题本
          extra: mistakeRecord
        }
      }).then(result => {
        console.log('朗读回答低分题目已自动添加到错题本:', result);
      }).catch(error => {
        console.error('自动添加错题失败:', error);
        // 静默失败，不影响练习流程
      });
    });

    console.log(`朗读短文并回答问题完成，共收集到 ${lowScoreQuestions.length} 个低分题目`);
  }
}); 