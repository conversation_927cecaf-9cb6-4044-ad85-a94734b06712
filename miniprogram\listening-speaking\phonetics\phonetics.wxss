/* 页面容器 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面头部 */
.page-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.header-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 选择模式区域 */
.mode-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.mode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.mode-radio {
  display: flex;
  gap: 40rpx;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.mode-text {
  font-size: 28rpx;
  color: #555;
}

/* 选择区域 */
.selection-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.selection-info {
  background: #f0f8ff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid #4CAF50;
}

.info-text {
  font-size: 24rpx;
  color: #4CAF50;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 8rpx;
  margin-bottom: 25rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #4CAF50;
  color: white;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: white;
}

/* 操作按钮区域 */
.selection-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 25rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  border: 2rpx solid #ddd;
  background: white;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  border-color: #2196F3;
  color: #2196F3;
}

.action-btn.secondary:hover {
  background: #2196F3;
  color: white;
}

.action-btn.small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 26rpx;
}

/* 音标网格 */
.phonetic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.phonetic-item {
  position: relative;
  background: #f8f9fa;
  border: 3rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 25rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.phonetic-item:hover {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.phonetic-item.selected {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-color: #4CAF50;
  color: white;
}

.phonetic-symbol {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.phonetic-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.phonetic-item.selected .phonetic-name,
.phonetic-item.selected .phonetic-example {
  color: rgba(255, 255, 255, 0.9);
}

.phonetic-example {
  font-size: 22rpx;
  color: #888;
  font-style: italic;
}

.selected-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-icon {
  font-size: 24rpx;
  color: white;
}

/* 已选择区域 */
.selected-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.selected-header {
  margin-bottom: 20rpx;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.selected-item {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  padding: 15rpx 20rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 24rpx;
}

.selected-symbol {
  font-weight: bold;
  font-size: 28rpx;
}

.selected-name {
  opacity: 0.9;
}

.selected-example {
  opacity: 0.7;
  font-style: italic;
}

/* 生成按钮 */
.generate-section {
  margin-bottom: 40rpx;
}

.generate-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  box-shadow: 0 10rpx 30rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.generate-btn:not(.disabled):hover {
  transform: translateY(-3rpx);
  box-shadow: 0 15rpx 40rpx rgba(76, 175, 80, 0.4);
}

.generate-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-icon.loading {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.btn-text {
  font-size: 32rpx;
}

/* 结果区域 */
.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.result-actions {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.result-content {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  border-left: 8rpx solid #4CAF50;
}

.result-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 帮助区域 */
.help-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.help-content {
  text-align: center;
}

.help-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.help-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.help-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  text-align: left;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.help-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.help-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
} 